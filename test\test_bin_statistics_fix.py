#!/usr/bin/env python3
"""
Test script to verify bin statistics fix in Bin_Summary sheet
"""

import os
import sys
import tempfile

# Add parent directory to path to import modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from full_map_processor import FullMapProcessor
from tsk_map_processor import TSKMapProcessor


def test_bin_statistics_extraction():
    """Test bin statistics extraction from TSKMapProcessor"""
    print("🧪 Testing Bin Statistics Extraction...")
    
    test_file = "test/3AC468-01-C5.map"
    if not os.path.exists(test_file):
        print(f"❌ Test file not found: {test_file}")
        return False
    
    try:
        # Create and process with TSKMapProcessor directly
        processor = TSKMapProcessor()
        
        if not processor.read_file(test_file):
            print("❌ Failed to read test file")
            return False
        
        if not processor.parse_file_header():
            print("❌ Failed to parse file header")
            return False
        
        if not processor.process_die_data():
            print("❌ Failed to process die data")
            return False
        
        # Get bin statistics using existing method
        bin_stats = processor.get_bin_statistics()
        
        print(f"✅ Bin statistics extracted: {len(bin_stats)} bins found")
        
        # Display bin statistics
        total_dies = 0
        for i, bin_stat in enumerate(bin_stats[:10]):  # Show first 10 bins
            bin_name = bin_stat['bin_name']
            quantity = bin_stat['quantity']
            yield_pct = bin_stat['yield_percentage']
            total_dies += quantity
            print(f"   {i+1:2d}. {bin_name}: {quantity:4d} dies ({yield_pct:5.2f}%)")
        
        print(f"   Total dies: {total_dies}")
        
        # Test the conversion logic used in FullMapProcessor
        bin_dict = {}
        for bin_stat in bin_stats:
            bin_name = bin_stat.get('bin_name', '')
            quantity = bin_stat.get('quantity', 0)
            
            if bin_name.startswith('bin'):
                try:
                    bin_num = int(bin_name[3:])  # Remove 'bin' prefix
                    bin_dict[bin_num] = quantity
                except ValueError:
                    continue
        
        print(f"✅ Converted to bin dictionary: {len(bin_dict)} bins")
        
        # Show some specific bins
        for bin_num in [0, 1, 2, 3, 4, 5]:
            count = bin_dict.get(bin_num, 0)
            print(f"   Bin {bin_num:2d}: {count:4d} dies")
        
        return len(bin_stats) > 0 and total_dies > 0
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_bin_summary_sheet_content():
    """Test Bin_Summary sheet contains correct bin data"""
    print("\n🧪 Testing Bin_Summary Sheet Content...")
    
    test_file = "test/3AC468-01-C5.map"
    if not os.path.exists(test_file):
        print(f"❌ Test file not found: {test_file}")
        return False
    
    try:
        # Create Full Map Processor
        processor = FullMapProcessor()
        processor.set_rotation_angle(0)
        processor.set_filter_empty(True)

        # Set output to test directory
        test_dir = os.path.dirname(os.path.abspath(__file__))
        processor.set_output_folder(test_dir)
        
        # Process file
        result = processor.process_multiple_files([test_file])
        
        if result and os.path.exists(result):
            print(f"✅ Excel file created: {result}")
            
            # Load and examine the Excel file
            from openpyxl import load_workbook
            wb = load_workbook(result)
            
            if "Bin_Summary" in wb.sheetnames:
                ws = wb["Bin_Summary"]
                
                # Check data in row 6 (first data row)
                sheet_name = ws.cell(row=6, column=1).value
                yield_value = ws.cell(row=6, column=2).value
                
                print(f"✅ Data row found - Sheet: {sheet_name}, Yield: {yield_value}")
                
                # Check bin columns (C00 to C128) - all 129 bins
                bin_data = []
                non_zero_bins = 0
                total_dies = 0

                for col in range(3, 132):  # Columns C to DD (bins 0-128)
                    bin_value = ws.cell(row=6, column=col).value
                    bin_num = col - 3

                    if bin_value and bin_value > 0:
                        bin_data.append((bin_num, bin_value))
                        non_zero_bins += 1
                        total_dies += bin_value
                
                print(f"✅ Bin data extracted:")
                for bin_num, count in bin_data:
                    print(f"   Bin {bin_num:2d}: {count:4d} dies")
                
                print(f"✅ Summary: {non_zero_bins} non-zero bins, {total_dies} total dies")
                
                # Verify against direct TSKMapProcessor results
                tsk_processor = TSKMapProcessor()
                tsk_processor.read_file(test_file)
                tsk_processor.parse_file_header()
                tsk_processor.process_die_data()
                
                direct_stats = tsk_processor.get_bin_statistics()
                direct_total = sum(stat['quantity'] for stat in direct_stats)
                
                print(f"✅ Direct TSKMapProcessor total: {direct_total} dies")
                
                if total_dies == direct_total:
                    print("✅ Bin_Summary data matches direct processor results")
                    wb.close()
                    return True
                else:
                    print(f"❌ Mismatch: Bin_Summary={total_dies}, Direct={direct_total}")
                    wb.close()
                    return False
            else:
                print("❌ Bin_Summary sheet not found")
                wb.close()
                return False
        else:
            print("❌ Failed to create Excel file")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_multiple_files_bin_summary():
    """Test Bin_Summary with multiple files"""
    print("\n🧪 Testing Multiple Files Bin_Summary...")
    
    test_file = "test/3AC468-01-C5.map"
    if not os.path.exists(test_file):
        print(f"❌ Test file not found: {test_file}")
        return False
    
    # Create temporary output folder
    with tempfile.TemporaryDirectory() as temp_dir:
        try:
            # Create Full Map Processor
            processor = FullMapProcessor()
            processor.set_rotation_angle(0)
            processor.set_filter_empty(True)
            processor.set_output_folder(temp_dir)
            
            # Process same file twice (simulating multiple files)
            file_list = [test_file, test_file]
            
            print(f"⚙️ Processing {len(file_list)} files...")
            
            result = processor.process_multiple_files(file_list)
            
            if result and os.path.exists(result):
                print(f"✅ Multi-file Excel created: {os.path.basename(result)}")
                
                # Examine Bin_Summary sheet
                from openpyxl import load_workbook
                wb = load_workbook(result)
                
                if "Bin_Summary" in wb.sheetnames:
                    ws = wb["Bin_Summary"]
                    
                    # Count data rows
                    data_rows = 0
                    for row in range(6, 20):
                        sheet_name = ws.cell(row=row, column=1).value
                        if sheet_name and sheet_name != "Average":
                            data_rows += 1
                            
                            # Check bin data for this row
                            bin_total = 0
                            for col in range(3, 13):  # First 10 bin columns
                                bin_value = ws.cell(row=row, column=col).value
                                if bin_value and bin_value > 0:
                                    bin_total += bin_value
                            
                            print(f"   Row {row}: {sheet_name} - {bin_total} total dies")
                        elif sheet_name == "Average":
                            print(f"   Row {row}: Average row found")
                            break
                    
                    print(f"✅ Found {data_rows} data rows in Bin_Summary")
                    
                    wb.close()
                    return data_rows > 0
                else:
                    print("❌ Bin_Summary sheet not found")
                    wb.close()
                    return False
            else:
                print("❌ Failed to create multi-file Excel")
                return False
                
        except Exception as e:
            print(f"❌ Error: {e}")
            return False


def main():
    """Run all bin statistics tests"""
    print("🚀 Bin Statistics Fix Verification")
    print("=" * 50)
    
    # Run tests
    test1 = test_bin_statistics_extraction()
    test2 = test_bin_summary_sheet_content()
    test3 = test_multiple_files_bin_summary()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"   Bin Statistics Extraction: {'✅ PASS' if test1 else '❌ FAIL'}")
    print(f"   Bin_Summary Sheet Content: {'✅ PASS' if test2 else '❌ FAIL'}")
    print(f"   Multiple Files Bin_Summary: {'✅ PASS' if test3 else '❌ FAIL'}")
    
    all_passed = test1 and test2 and test3
    print(f"\n🎯 Overall: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
    
    if all_passed:
        print("\n🎉 Bin statistics fix verified successfully!")
        print("   Bin_Summary sheet now correctly shows bin quantities")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
