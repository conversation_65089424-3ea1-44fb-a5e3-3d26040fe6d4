#!/usr/bin/env python3
"""
Test Bin Name Format - Verify the new bin name format without "bin" prefix
"""

import sys
import os

# Add parent directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

from tsk_map_processor import TSKMapProcessor
from config_reader import ConfigReader
from excel_output import create_excel_output


def test_bin_name_formatting():
    """Test bin name formatting with and without config"""
    print("Testing Bin Name Formatting")
    print("=" * 40)
    
    config_path = "3509_CP1_program_bin.xlsx"
    
    if not os.path.exists(config_path):
        print(f"❌ Config file not found: {config_path}")
        return False
    
    try:
        # Test with config file
        reader = ConfigReader()
        if reader.read_config_file(config_path):
            print(f"✅ Config file loaded successfully")
            
            # Test various bin numbers
            test_bins = [2, 64, 89, 91, 127, 999]  # Mix of mapped and unmapped
            
            print(f"\nBin Name Formatting Tests:")
            print(f"{'Bin#':<6} {'Has Mapping':<12} {'Old Format':<25} {'New Format':<25}")
            print("-" * 68)
            
            for bin_num in test_bins:
                has_mapping = bin_num in reader.get_all_bin_mappings()
                old_format = f"bin{bin_num}({reader.get_bin_name(bin_num)})" if has_mapping else f"bin{bin_num}"
                new_format = reader.get_formatted_bin_name(bin_num)
                
                print(f"{bin_num:<6} {str(has_mapping):<12} {old_format:<25} {new_format:<25}")
            
            return True
        else:
            print(f"❌ Failed to load config file")
            return False
            
    except Exception as e:
        print(f"❌ Error testing bin name formatting: {e}")
        return False


def test_excel_output_with_new_format(tsk_filepath):
    """Test Excel output with new bin name format"""
    print(f"\nTesting Excel Output with New Format")
    print("=" * 50)
    
    config_path = "3509_CP1_program_bin.xlsx"
    
    if not os.path.exists(tsk_filepath):
        print(f"❌ TSK file not found: {tsk_filepath}")
        return False
    
    if not os.path.exists(config_path):
        print(f"❌ Config file not found: {config_path}")
        return False
    
    try:
        # Load TSK file
        processor = TSKMapProcessor()
        
        if not processor.read_file(tsk_filepath):
            print("❌ Failed to read TSK file")
            return False
        
        if not processor.parse_file_header():
            print("❌ Failed to parse TSK header")
            return False
        
        if not processor.process_die_data():
            print("❌ Failed to process die data")
            return False
        
        # Load config file
        config_reader = ConfigReader()
        if not config_reader.read_config_file(config_path):
            print("❌ Failed to load config file")
            return False
        
        print(f"✅ Both files loaded successfully")
        
        # Get bin statistics
        bin_stats = processor.get_bin_statistics()
        
        print(f"\nBin Statistics with New Format:")
        print(f"{'Rank':<4} {'New Format':<30} {'Quantity':<8} {'Yield%':<8}")
        print("-" * 50)
        
        for i, bin_data in enumerate(bin_stats[:10], 1):  # Show top 10
            bin_number = int(bin_data['bin_name'].replace('bin', ''))
            new_format = config_reader.get_formatted_bin_name(bin_number)
            quantity = bin_data['quantity']
            yield_pct = bin_data['yield_percentage']
            
            print(f"{i:<4} {new_format:<30} {quantity:<8} {yield_pct:<8.2f}")
        
        # Create Excel output with new format
        output_filename = "test_new_bin_format_output.xlsx"
        
        if create_excel_output(processor, "TestSheet", 0, output_filename, tsk_filepath, config_reader):
            print(f"\n✅ Excel output created: {output_filename}")
            
            if os.path.exists(output_filename):
                file_size = os.path.getsize(output_filename)
                print(f"  File size: {file_size:,} bytes")
                
                print(f"\nExpected Excel A14+ Content (New Format):")
                for i, bin_data in enumerate(bin_stats[:5], 14):
                    bin_number = int(bin_data['bin_name'].replace('bin', ''))
                    new_format = config_reader.get_formatted_bin_name(bin_number)
                    print(f"  A{i}: '{new_format}' ({bin_data['quantity']} dies)")
                
                return True
            else:
                print("❌ Output file not found")
                return False
        else:
            print("❌ Failed to create Excel output")
            return False
            
    except Exception as e:
        print(f"❌ Error in Excel output test: {e}")
        return False


def test_without_config_file(tsk_filepath):
    """Test Excel output without config file (should show just numbers)"""
    print(f"\nTesting Without Config File")
    print("=" * 40)
    
    if not os.path.exists(tsk_filepath):
        print(f"❌ TSK file not found: {tsk_filepath}")
        return False
    
    try:
        # Load TSK file
        processor = TSKMapProcessor()
        
        if not processor.read_file(tsk_filepath):
            print("❌ Failed to read TSK file")
            return False
        
        if not processor.parse_file_header():
            print("❌ Failed to parse TSK header")
            return False
        
        if not processor.process_die_data():
            print("❌ Failed to process die data")
            return False
        
        print(f"✅ TSK file loaded successfully")
        
        # Get bin statistics
        bin_stats = processor.get_bin_statistics()
        
        print(f"\nBin Statistics without Config (Numbers Only):")
        print(f"{'Rank':<4} {'Format':<15} {'Quantity':<8} {'Yield%':<8}")
        print("-" * 35)
        
        for i, bin_data in enumerate(bin_stats[:5], 1):  # Show top 5
            bin_number = int(bin_data['bin_name'].replace('bin', ''))
            format_without_config = str(bin_number)  # Just the number
            quantity = bin_data['quantity']
            yield_pct = bin_data['yield_percentage']
            
            print(f"{i:<4} {format_without_config:<15} {quantity:<8} {yield_pct:<8.2f}")
        
        # Create Excel output without config
        output_filename = "test_no_config_format_output.xlsx"
        
        if create_excel_output(processor, "TestSheet", 0, output_filename, tsk_filepath, None):
            print(f"\n✅ Excel output created without config: {output_filename}")
            
            print(f"\nExpected Excel A14+ Content (Numbers Only):")
            for i, bin_data in enumerate(bin_stats[:5], 14):
                bin_number = int(bin_data['bin_name'].replace('bin', ''))
                print(f"  A{i}: '{bin_number}' ({bin_data['quantity']} dies)")
            
            return True
        else:
            print("❌ Failed to create Excel output")
            return False
            
    except Exception as e:
        print(f"❌ Error in no-config test: {e}")
        return False


def main():
    """Main test function"""
    if len(sys.argv) < 2:
        print("Bin Name Format Test")
        print("Usage: python test_bin_name_format.py <tsk_file_path>")
        print("Example: python test_bin_name_format.py 3AA111-01-B4")
        return
    
    tsk_filepath = sys.argv[1]
    
    print("TSK/MAP Bin Name Format Test")
    print("Testing new bin name format without 'bin' prefix")
    print("=" * 70)
    
    # Test 1: Bin name formatting
    if not test_bin_name_formatting():
        print("❌ Bin name formatting test failed")
        return
    
    # Test 2: Excel output with config
    if not test_excel_output_with_new_format(tsk_filepath):
        print("❌ Excel output with config test failed")
        return
    
    # Test 3: Excel output without config
    if not test_without_config_file(tsk_filepath):
        print("❌ Excel output without config test failed")
        return
    
    print("\n" + "=" * 70)
    print("🎉 Bin Name Format Test Completed!")
    print("\nFormat Changes:")
    print("✅ Old format: bin2, bin64(TEST OPEN SHORT TEST), bin127(PASS)")
    print("✅ New format: 2, 64(TEST OPEN SHORT TEST), 127(PASS)")
    print("✅ Removed 'bin' prefix from all bin names")
    print("✅ With config: '数字(名称)' format")
    print("✅ Without config: '数字' format")
    
    print(f"\nExcel A14+ Column Content:")
    print(f"• With config file: 2, 9, 50, 5, 64(TEST OPEN SHORT TEST), ...")
    print(f"• Without config: 2, 9, 50, 5, 64, ...")


if __name__ == "__main__":
    main()
