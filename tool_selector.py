#!/usr/bin/env python3
"""
Tool Selector - Choose between AB Map Tool and Full Map Tool
"""

import tkinter as tk
from tkinter import ttk
import os


class ToolSelector:
    """Tool selection dialog for choosing between AB Map and Full Map tools"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("TSK/MAP File Processor - Tool Selection")
        self.root.geometry("500x300")
        self.root.resizable(False, False)

        # Center the window
        self.center_window()

        # Variables
        self.selected_tool = None
        self.window_closed = False

        # Set up window close protocol
        self.root.protocol("WM_DELETE_WINDOW", self.on_window_close)

        # Create GUI elements
        self.create_widgets()
    
    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_widgets(self):
        """Create the tool selection interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="TSK/MAP File Processor", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, pady=(0, 10))
        
        subtitle_label = ttk.Label(main_frame, text="Please select a tool to continue:", 
                                  font=("Arial", 10))
        subtitle_label.grid(row=1, column=0, pady=(0, 20))
        
        # Tool selection frame
        tool_frame = ttk.LabelFrame(main_frame, text="Available Tools", padding="15")
        tool_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        tool_frame.columnconfigure(0, weight=1)
        
        # AB Map Tool option
        ab_map_frame = ttk.Frame(tool_frame)
        ab_map_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        ab_map_frame.columnconfigure(1, weight=1)
        
        ab_map_button = ttk.Button(ab_map_frame, text="AB Map Tool", 
                                  command=self.select_ab_map_tool, width=15)
        ab_map_button.grid(row=0, column=0, padx=(0, 10))
        
        ab_map_desc = ttk.Label(ab_map_frame, 
                               text="Process single TSK/MAP file with Amap/Bmap sheet selection")
        ab_map_desc.grid(row=0, column=1, sticky=tk.W)
        
        # Full Map Tool option
        full_map_frame = ttk.Frame(tool_frame)
        full_map_frame.grid(row=1, column=0, sticky=(tk.W, tk.E))
        full_map_frame.columnconfigure(1, weight=1)
        
        full_map_button = ttk.Button(full_map_frame, text="Full Map Tool", 
                                    command=self.select_full_map_tool, width=15)
        full_map_button.grid(row=0, column=0, padx=(0, 10))
        
        full_map_desc = ttk.Label(full_map_frame, 
                                 text="Process multiple MAP files, generate one Excel with multiple sheets")
        full_map_desc.grid(row=0, column=1, sticky=tk.W)
        
        # Features comparison
        features_frame = ttk.LabelFrame(main_frame, text="Feature Comparison", padding="15")
        features_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        
        # Create comparison table
        headers = ["Feature", "AB Map Tool", "Full Map Tool"]
        features = [
            ("Input Files", "Single TSK/MAP file", "Multiple MAP files"),
            ("Output", "Single Excel file", "Single Excel with multiple sheets"),
            ("Sheet Selection", "Amap or Bmap", "All maps as separate sheets"),
            ("Configuration", "Excel config file", "Excel config file"),
            ("Bin Name Mapping", "✓ Supported", "✓ Supported"),
            ("Rotation Options", "✓ 0°/90°/180°/270°", "✓ 0°/90°/180°/270°")
        ]
        
        for i, header in enumerate(headers):
            label = ttk.Label(features_frame, text=header, font=("Arial", 9, "bold"))
            label.grid(row=0, column=i, padx=5, pady=2, sticky=tk.W)
        
        for row, (feature, ab_map, full_map) in enumerate(features, 1):
            ttk.Label(features_frame, text=feature).grid(row=row, column=0, padx=5, pady=2, sticky=tk.W)
            ttk.Label(features_frame, text=ab_map).grid(row=row, column=1, padx=5, pady=2, sticky=tk.W)
            ttk.Label(features_frame, text=full_map).grid(row=row, column=2, padx=5, pady=2, sticky=tk.W)
        
        # Exit button
        exit_button = ttk.Button(main_frame, text="Exit", command=self.root.quit)
        exit_button.grid(row=4, column=0, pady=(10, 0))
    
    def select_ab_map_tool(self):
        """Select AB Map Tool"""
        self.selected_tool = "ab_map"
        self.root.quit()
    
    def select_full_map_tool(self):
        """Select Full Map Tool"""
        self.selected_tool = "full_map"
        self.root.quit()

    def on_window_close(self):
        """Handle window close event"""
        self.window_closed = True
        self.selected_tool = None
        self.root.quit()
    
    def show(self):
        """Show the tool selector and return the selected tool"""
        try:
            self.root.mainloop()
            tool = self.selected_tool

            # Safe window destruction
            if not self.window_closed:
                try:
                    self.root.destroy()
                except tk.TclError:
                    # Window already destroyed
                    pass

            return tool

        except Exception as e:
            # Handle any unexpected errors
            print(f"Error in tool selector: {e}")
            try:
                if not self.window_closed:
                    self.root.destroy()
            except:
                pass
            return None


def main():
    """Main function for testing"""
    selector = ToolSelector()
    selected_tool = selector.show()
    
    if selected_tool == "ab_map":
        print("Starting AB Map Tool...")
        # Import and start AB Map Tool
        from tsk_map_gui import TSKMapGUI
        import tkinter as tk
        
        root = tk.Tk()
        TSKMapGUI(root)
        root.mainloop()
        
    elif selected_tool == "full_map":
        print("Starting Full Map Tool...")
        # Import and start Full Map Tool
        from full_map_gui import FullMapGUI
        import tkinter as tk
        
        root = tk.Tk()
        FullMapGUI(root)
        root.mainloop()
        
    else:
        print("No tool selected. Exiting...")


if __name__ == "__main__":
    main()
