#!/usr/bin/env python3
"""
Full Map Processor - Process multiple MAP files and generate Excel with multiple sheets
"""

import os
from typing import List, Dict, Any, Optional
from datetime import datetime
from openpyxl import Workbook
from tsk_map_processor import TSKMapProcessor
from excel_output import ExcelOutputHandler
from config_reader import ConfigReader


class FullMapProcessor:
    """Processes multiple MAP files and generates a single Excel file with multiple sheets"""
    
    def __init__(self):
        self.rotation_angle = 0
        self.filter_empty = True
        self.sort_by_quantity = True  # New: Sort bins by quantity (default: True)
        self.config_reader = None
        self.processors = {}  # filename -> TSKMapProcessor
        self.output_folder = None
        
    def set_rotation_angle(self, angle: int):
        """Set rotation angle for all maps"""
        self.rotation_angle = angle
    
    def set_filter_empty(self, filter_empty: bool):
        """Set whether to filter empty areas"""
        self.filter_empty = filter_empty

    def set_sort_by_quantity(self, sort_by_quantity: bool):
        """Set whether to sort bins by quantity (True) or by bin number (False)"""
        self.sort_by_quantity = sort_by_quantity

    def set_config_reader(self, config_reader: Optional[ConfigReader]):
        """Set configuration reader for bin name mapping"""
        self.config_reader = config_reader

    def set_output_folder(self, output_folder: Optional[str]):
        """Set output folder for generated files"""
        self.output_folder = output_folder
    
    def process_multiple_files(self, file_paths: List[str]) -> Optional[str]:
        """
        Process multiple MAP files and generate Excel with multiple sheets
        Returns the output filename if successful, None otherwise
        """
        if not file_paths:
            print("No files to process")
            return None
        
        print(f"Processing {len(file_paths)} MAP files...")
        
        # Process each file
        successful_processors = {}
        
        for file_path in file_paths:
            filename = os.path.basename(file_path)
            print(f"Processing: {filename}")
            
            try:
                # Create processor for this file
                processor = TSKMapProcessor()
                
                # Read and parse file
                if not processor.read_file(file_path):
                    print(f"❌ Failed to read file: {filename}")
                    continue
                
                if not processor.parse_file_header():
                    print(f"❌ Failed to parse header: {filename}")
                    continue
                
                if not processor.process_die_data():
                    print(f"❌ Failed to process die data: {filename}")
                    continue
                
                # Store successful processor
                successful_processors[filename] = {
                    'processor': processor,
                    'file_path': file_path
                }
                
                print(f"✅ Successfully processed: {filename}")
                
            except Exception as e:
                print(f"❌ Error processing {filename}: {e}")
                continue
        
        if not successful_processors:
            print("❌ No files were successfully processed")
            return None
        
        print(f"✅ Successfully processed {len(successful_processors)} files")
        
        # Generate Excel file with multiple sheets
        return self._create_multi_sheet_excel(successful_processors, file_paths)
    
    def _create_multi_sheet_excel(self, processors: Dict[str, Dict], original_file_paths: List[str]) -> Optional[str]:
        """Create Excel file with multiple sheets"""
        try:
            # Generate output filename based on first file and timestamp
            output_filename = self._generate_output_filename(original_file_paths, len(processors))
            
            print(f"Creating Excel file: {output_filename}")
            
            # Create workbook
            workbook = Workbook()
            
            # Remove default sheet
            if workbook.active:
                workbook.remove(workbook.active)

            # Create Bin_Summary sheet first
            bin_summary_sheet = workbook.create_sheet(title="Bin_Summary")
            sheet_count = 1

            # Process each file and create a sheet
            for filename, data in processors.items():
                processor = data['processor']
                file_path = data['file_path']
                
                # Create sheet name from filename (remove extension)
                sheet_name = os.path.splitext(filename)[0]
                
                # Ensure sheet name is valid for Excel (max 31 chars, no special chars)
                sheet_name = self._sanitize_sheet_name(sheet_name)
                
                print(f"Creating sheet: {sheet_name}")
                
                try:
                    # Create worksheet
                    worksheet = workbook.create_sheet(title=sheet_name)
                    
                    # Create Excel handler for this sheet
                    excel_handler = ExcelOutputHandler()
                    excel_handler.workbook = workbook
                    excel_handler.worksheet = worksheet
                    
                    # Get rotated data
                    rotated_data = self._get_rotated_data(processor)
                    
                    # Write data to worksheet
                    if excel_handler.write_rotated_data(rotated_data, self.rotation_angle,
                                                      sheet_name, processor, self.config_reader,
                                                      self.sort_by_quantity):
                        sheet_count += 1
                        print(f"✅ Sheet created successfully: {sheet_name}")
                    else:
                        print(f"❌ Failed to write data to sheet: {sheet_name}")
                        # Remove failed sheet
                        workbook.remove(worksheet)
                        
                except Exception as e:
                    print(f"❌ Error creating sheet {sheet_name}: {e}")
                    continue
            
            if sheet_count == 0:
                print("❌ No sheets were created successfully")
                return None

            # Fill Bin_Summary sheet with data
            self._create_bin_summary_sheet(bin_summary_sheet, processors, workbook)

            # Save workbook
            workbook.save(output_filename)
            workbook.close()
            
            print(f"✅ Excel file saved: {output_filename}")
            print(f"   Sheets created: {sheet_count}")
            
            return output_filename
            
        except Exception as e:
            print(f"❌ Error creating Excel file: {e}")
            return None

    def _generate_output_filename(self, file_paths: List[str], processor_count: int) -> str:
        """Generate output filename based on first file and timestamp"""
        try:
            if not file_paths:
                # Fallback filename with timestamp
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                rotation_suffix = f"_R{self.rotation_angle}"
                filename = f"all_maps_summary{rotation_suffix}_{timestamp}.xlsx"

                # Use output folder if specified
                if self.output_folder and os.path.exists(self.output_folder):
                    return os.path.join(self.output_folder, filename)
                else:
                    return filename

            # Get first file path
            first_file_path = file_paths[0]
            first_filename = os.path.basename(first_file_path)

            # Extract base name (before first "-" if exists)
            if "-" in first_filename:
                base_name = first_filename.split("-")[0]
            else:
                # If no "-", use filename without extension
                base_name = os.path.splitext(first_filename)[0]

            # Generate timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Generate rotation suffix (always include rotation angle)
            rotation_suffix = f"_R{self.rotation_angle}"

            # Generate filename
            if processor_count == 1:
                filename = f"{base_name}_full_map{rotation_suffix}_{timestamp}.xlsx"
            else:
                filename = f"{base_name}_all_maps_summary{rotation_suffix}_{timestamp}.xlsx"

            # Use output folder if specified
            if self.output_folder and os.path.exists(self.output_folder):
                output_filename = os.path.join(self.output_folder, filename)
            else:
                output_filename = filename

            return output_filename

        except Exception as e:
            print(f"Error generating filename: {e}")
            # Fallback filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            rotation_suffix = f"_R{self.rotation_angle}"
            filename = f"all_maps_summary{rotation_suffix}_{timestamp}.xlsx"

            # Use output folder if specified
            if self.output_folder and os.path.exists(self.output_folder):
                return os.path.join(self.output_folder, filename)
            else:
                return filename

    def _sanitize_sheet_name(self, name: str) -> str:
        """Sanitize sheet name for Excel compatibility"""
        # Remove invalid characters
        invalid_chars = ['\\', '/', '*', '?', ':', '[', ']']
        for char in invalid_chars:
            name = name.replace(char, '_')
        
        # Limit length to 31 characters
        if len(name) > 31:
            name = name[:31]
        
        # Ensure name is not empty
        if not name.strip():
            name = "Sheet"
        
        return name.strip()
    
    def _get_rotated_data(self, processor: TSKMapProcessor):
        """Get rotated data from processor (reuse existing logic)"""
        if not processor.map_data:
            return []
        
        data = processor.map_data
        
        if self.rotation_angle == 0:
            return data
        elif self.rotation_angle == 90:
            return self._rotate_90(data)
        elif self.rotation_angle == 180:
            return self._rotate_180(data)
        elif self.rotation_angle == 270:
            return self._rotate_270(data)
        else:
            return data
    
    def _rotate_90(self, data):
        """Rotate data 90 degrees clockwise"""
        if not data:
            return []
        
        rows = len(data)
        cols = len(data[0]) if rows > 0 else 0
        
        rotated = []
        for j in range(cols):
            new_row = []
            for i in range(rows - 1, -1, -1):
                new_row.append(data[i][j])
            rotated.append(new_row)
        
        return rotated
    
    def _rotate_180(self, data):
        """Rotate data 180 degrees"""
        if not data:
            return []
        
        rotated = []
        for i in range(len(data) - 1, -1, -1):
            new_row = []
            for j in range(len(data[i]) - 1, -1, -1):
                new_row.append(data[i][j])
            rotated.append(new_row)
        
        return rotated
    
    def _rotate_270(self, data):
        """Rotate data 270 degrees clockwise (90 degrees counter-clockwise)"""
        if not data:
            return []
        
        rows = len(data)
        cols = len(data[0]) if rows > 0 else 0
        
        rotated = []
        for j in range(cols - 1, -1, -1):
            new_row = []
            for i in range(rows):
                new_row.append(data[i][j])
            rotated.append(new_row)
        
        return rotated

    def _create_bin_summary_sheet(self, worksheet, processors: Dict[str, Dict], workbook):
        """Create Bin_Summary sheet with enhanced header information and bin statistics for each map"""
        try:
            from openpyxl.styles import Font, Alignment, PatternFill
            from openpyxl.utils import get_column_letter

            # Create header information in rows 1-2
            self._create_summary_header(worksheet, processors)

            # Set up headers in row 5 (A5 onwards)
            headers = ["LotID-waferID", "Yield(%)"]

            # Add bin columns C00 to C128
            for i in range(129):  # 0 to 128
                headers.append(f"C{i:02d}")

            # Write beautifully formatted headers in row 5
            self._format_bin_table_headers(worksheet, headers)

            # Get sheet names from workbook (excluding Bin_Summary)
            sheet_names = [sheet.title for sheet in workbook.worksheets if sheet.title != "Bin_Summary"]

            # Fill data for each sheet starting from row 6 (A6 onwards)
            current_row = 6
            bin_data_rows = []  # Store data for average calculation

            for sheet_name in sheet_names:
                # Find corresponding processor
                processor_data = None
                for filename, data in processors.items():
                    if self._sanitize_sheet_name(os.path.splitext(filename)[0]) == sheet_name:
                        processor_data = data
                        break

                if not processor_data:
                    continue

                processor = processor_data['processor']

                # Write sheet name in column A
                worksheet.cell(row=current_row, column=1, value=sheet_name)

                # Get test statistics using existing method
                test_stats = processor.get_test_statistics() if hasattr(processor, 'get_test_statistics') else {}
                total_tested = test_stats.get('total_tested', 0)
                pass_count = test_stats.get('pass_count', 0)
                yield_percent = test_stats.get('yield_percentage', 0.0)

                # Write yield percentage in column B
                yield_cell = worksheet.cell(row=current_row, column=2, value=yield_percent / 100)  # Convert to decimal for percentage format
                yield_cell.number_format = '0.00%'

                # Get bin statistics (sorting doesn't affect Bin_Summary as it uses fixed C00-C128 order)
                bin_stats = self._get_bin_statistics_from_processor(processor, self.sort_by_quantity)
                row_data = [yield_percent / 100]  # Store for average calculation (as decimal)

                # Fill bin counts (C00 to C128)
                for bin_num in range(129):  # 0 to 128
                    bin_count = bin_stats.get(bin_num, 0)
                    worksheet.cell(row=current_row, column=bin_num + 3, value=bin_count)
                    row_data.append(bin_count)

                bin_data_rows.append(row_data)
                current_row += 1

            # Add empty row
            current_row += 1

            # Add Average row
            worksheet.cell(row=current_row, column=1, value="Average")
            avg_cell = worksheet.cell(row=current_row, column=1)
            avg_cell.font = Font(bold=True)

            # Calculate averages for each column
            if bin_data_rows:
                num_rows = len(bin_data_rows)

                # Average yield (column B) - Green color for yield percentage
                avg_yield = sum(row[0] for row in bin_data_rows) / num_rows
                yield_avg_cell = worksheet.cell(row=current_row, column=2, value=avg_yield)
                yield_avg_cell.number_format = '0.00%'
                yield_avg_cell.font = Font(bold=True, color='28A745')  # Green color for yield percentage

                # Average bin counts (columns C onwards)
                for col_idx in range(1, 130):  # 1 to 129 (for bins 0-128)
                    avg_value = sum(row[col_idx] for row in bin_data_rows) / num_rows
                    avg_cell = worksheet.cell(row=current_row, column=col_idx + 2, value=avg_value)
                    avg_cell.number_format = '0.00'
                    avg_cell.font = Font(bold=True)

            # Apply beautiful formatting to all data rows
            if current_row > 6:  # Only format if we have data rows
                self._format_data_rows(worksheet, 6, current_row - 2)  # Exclude empty row and average row

            # Format the average row specially
            self._format_average_row(worksheet, current_row)

            print("✅ Bin_Summary sheet created with beautiful formatting")

        except Exception as e:
            print(f"❌ Error creating Bin_Summary sheet: {e}")

    def _get_bin_statistics_from_processor(self, processor, sort_by_quantity=True) -> Dict[int, int]:
        """Extract bin statistics from processor using existing get_bin_statistics method"""
        try:
            bin_stats = {}

            # Use the existing get_bin_statistics method from TSKMapProcessor
            if hasattr(processor, 'get_bin_statistics'):
                bin_stats_list = processor.get_bin_statistics(sort_by_quantity)

                # Convert from list of dicts to dict[bin_number, count]
                for bin_stat in bin_stats_list:
                    bin_name = bin_stat.get('bin_name', '')
                    quantity = bin_stat.get('quantity', 0)

                    # Extract bin number from bin_name (e.g., "bin1" -> 1)
                    if bin_name.startswith('bin'):
                        try:
                            bin_num = int(bin_name[3:])  # Remove 'bin' prefix
                            bin_stats[bin_num] = quantity
                        except ValueError:
                            continue

            return bin_stats

        except Exception as e:
            print(f"Warning: Error extracting bin statistics: {e}")
            return {}

    def _setup_summary_styles(self):
        """Setup professional styling for Bin_Summary sheet"""
        from openpyxl.styles import Font, Alignment, PatternFill, Border, Side

        # Define color scheme - Professional blue and gray theme
        self.colors = {
            'primary_blue': '2E75B6',      # Professional blue for headers
            'light_blue': 'D6EAF8',        # Light blue for header backgrounds
            'accent_green': '28A745',       # Green for positive values (yield, pass)
            'accent_red': 'DC3545',         # Red for negative values (fail)
            'neutral_gray': 'F8F9FA',       # Light gray for alternating rows
            'dark_gray': '495057',          # Dark gray for text
            'white': 'FFFFFF'               # White background
        }

        # Define fonts - Using Calibri for readability and Segoe UI for headers
        self.fonts = {
            'header_large': Font(
                name='Segoe UI',
                size=14,
                bold=True,
                color='FFFFFF'  # White text on blue background
            ),
            'header_medium': Font(
                name='Segoe UI',
                size=12,
                bold=True,
                color=self.colors['primary_blue']
            ),
            'data_normal': Font(
                name='Calibri',
                size=11,
                color=self.colors['dark_gray']
            ),
            'data_bold': Font(
                name='Calibri',
                size=11,
                bold=True,
                color=self.colors['dark_gray']
            ),
            'yield_good': Font(
                name='Calibri',
                size=11,
                bold=True,
                color=self.colors['accent_green']
            ),
            'fail_count': Font(
                name='Calibri',
                size=11,
                bold=True,
                color=self.colors['accent_red']
            )
        }

        # Define fills
        self.fills = {
            'header_primary': PatternFill(
                start_color=self.colors['primary_blue'],
                end_color=self.colors['primary_blue'],
                fill_type='solid'
            ),
            'header_light': PatternFill(
                start_color=self.colors['light_blue'],
                end_color=self.colors['light_blue'],
                fill_type='solid'
            ),
            'data_alternate': PatternFill(
                start_color=self.colors['neutral_gray'],
                end_color=self.colors['neutral_gray'],
                fill_type='solid'
            ),
            'white': PatternFill(
                start_color=self.colors['white'],
                end_color=self.colors['white'],
                fill_type='solid'
            )
        }

        # Define borders
        thin_border = Side(border_style='thin', color='CCCCCC')
        medium_border = Side(border_style='medium', color=self.colors['primary_blue'])

        self.borders = {
            'thin_all': Border(
                left=thin_border, right=thin_border,
                top=thin_border, bottom=thin_border
            ),
            'medium_all': Border(
                left=medium_border, right=medium_border,
                top=medium_border, bottom=medium_border
            ),
            'bottom_medium': Border(bottom=medium_border)
        }

        # Define alignments
        self.alignments = {
            'center': Alignment(horizontal='center', vertical='center'),
            'left': Alignment(horizontal='left', vertical='center'),
            'right': Alignment(horizontal='right', vertical='center')
        }

    def _create_summary_header(self, worksheet, processors: Dict[str, Dict]):
        """Create beautifully formatted summary header information in rows 1-2"""
        try:
            from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
            from openpyxl.utils import get_column_letter

            # Calculate summary statistics
            total_maps = len(processors)
            total_tested_dies = 0
            total_pass_dies = 0

            # Get first map filename for Lot No extraction
            first_filename = ""
            if processors:
                first_filename = list(processors.keys())[0]

            # Extract Lot No (content before first "-" in filename)
            lot_no = ""
            if first_filename and "-" in first_filename:
                lot_no = first_filename.split("-")[0]

            # Calculate totals from all processors
            for filename, data in processors.items():
                processor = data['processor']
                if hasattr(processor, 'get_test_statistics'):
                    stats = processor.get_test_statistics()
                    total_tested_dies += stats.get('total_tested', 0)
                    total_pass_dies += stats.get('pass_count', 0)

            # Calculate overall yield
            overall_yield = (total_pass_dies / total_tested_dies * 100) if total_tested_dies > 0 else 0.0
            total_fail_dies = total_tested_dies - total_pass_dies

            # Get device name and vendor from config if available
            device_name = ""
            vendor_name = ""
            if self.config_reader:
                device_name = self.config_reader.get_device_name()
                vendor_name = self.config_reader.get_vendor_name()

            # Define professional styling
            self._setup_summary_styles()

            # Create beautifully formatted Row 1 - Device and Lot Information
            self._create_formatted_row1(worksheet, device_name, lot_no, total_maps, vendor_name)

            # Create beautifully formatted Row 2 - Test Statistics
            self._create_formatted_row2(worksheet, total_tested_dies, total_pass_dies, overall_yield, total_fail_dies)

            # Add thick black border around header area (A1:L2)
            self._add_header_border(worksheet)

            # Set column widths for better readability
            self._set_optimal_column_widths(worksheet)

            print(f"✅ Summary header created: {total_maps} maps, {total_tested_dies} tested, {total_pass_dies} pass, {overall_yield:.2f}% yield")

        except Exception as e:
            print(f"❌ Error creating summary header: {e}")

    def _create_formatted_row1(self, worksheet, device_name, lot_no, total_maps, vendor_name):
        """Create beautifully formatted Row 1 - Device and Lot Information"""
        try:
            # Row 1 - Device and Lot Information with professional styling

            # A1&B1: Device Name (Header)
            worksheet.merge_cells('A1:B1')
            cell_a1 = worksheet['A1']
            cell_a1.value = "Device Name"
            cell_a1.font = self.fonts['header_large']
            cell_a1.alignment = self.alignments['center']
            cell_a1.fill = self.fills['header_primary']
            cell_a1.border = self.borders['medium_all']

            # C1&D1: Device Name Value
            worksheet.merge_cells('C1:D1')
            cell_c1 = worksheet['C1']
            cell_c1.value = device_name or "N/A"
            cell_c1.font = self.fonts['data_bold']
            cell_c1.alignment = self.alignments['center']
            cell_c1.fill = self.fills['white']
            cell_c1.border = self.borders['thin_all']

            # E1: Lot No (Header)
            cell_e1 = worksheet['E1']
            cell_e1.value = "Lot No"
            cell_e1.font = self.fonts['header_large']
            cell_e1.alignment = self.alignments['center']
            cell_e1.fill = self.fills['header_primary']
            cell_e1.border = self.borders['medium_all']

            # F1&G1: Lot No Value
            worksheet.merge_cells('F1:G1')
            cell_f1 = worksheet['F1']
            cell_f1.value = lot_no or "N/A"
            cell_f1.font = self.fonts['data_bold']
            cell_f1.alignment = self.alignments['center']
            cell_f1.fill = self.fills['white']
            cell_f1.border = self.borders['thin_all']

            # H1: Total pcs (Header)
            cell_h1 = worksheet['H1']
            cell_h1.value = "Total pcs"
            cell_h1.font = self.fonts['header_large']
            cell_h1.alignment = self.alignments['center']
            cell_h1.fill = self.fills['header_primary']
            cell_h1.border = self.borders['medium_all']

            # I1: Total maps count
            cell_i1 = worksheet['I1']
            cell_i1.value = total_maps
            cell_i1.font = self.fonts['data_bold']
            cell_i1.alignment = self.alignments['center']
            cell_i1.fill = self.fills['white']
            cell_i1.border = self.borders['thin_all']

            # J1: VENDOR (Header)
            cell_j1 = worksheet['J1']
            cell_j1.value = "VENDOR"
            cell_j1.font = self.fonts['header_large']
            cell_j1.alignment = self.alignments['center']
            cell_j1.fill = self.fills['header_primary']
            cell_j1.border = self.borders['medium_all']

            # K1&L1: Vendor Value
            worksheet.merge_cells('K1:L1')
            cell_k1 = worksheet['K1']
            cell_k1.value = vendor_name or "N/A"
            cell_k1.font = self.fonts['data_bold']
            cell_k1.alignment = self.alignments['center']
            cell_k1.fill = self.fills['white']
            cell_k1.border = self.borders['thin_all']

        except Exception as e:
            print(f"❌ Error formatting row 1: {e}")

    def _create_formatted_row2(self, worksheet, total_tested, total_pass, overall_yield, total_fail):
        """Create beautifully formatted Row 2 - Test Statistics"""
        try:
            # Row 2 - Test Statistics with color-coded values

            # A2&B2: Total Tested (Header)
            worksheet.merge_cells('A2:B2')
            cell_a2 = worksheet['A2']
            cell_a2.value = "Total Tested"
            cell_a2.font = self.fonts['header_medium']
            cell_a2.alignment = self.alignments['center']
            cell_a2.fill = self.fills['header_light']
            cell_a2.border = self.borders['thin_all']

            # C2&D2: Total tested dies
            worksheet.merge_cells('C2:D2')
            cell_c2 = worksheet['C2']
            cell_c2.value = f"{total_tested:,}"  # Add thousand separators
            cell_c2.font = self.fonts['data_bold']
            cell_c2.alignment = self.alignments['center']
            cell_c2.fill = self.fills['white']
            cell_c2.border = self.borders['thin_all']

            # E2: Pass Dice (Header)
            cell_e2 = worksheet['E2']
            cell_e2.value = "Pass Dice"
            cell_e2.font = self.fonts['header_medium']
            cell_e2.alignment = self.alignments['center']
            cell_e2.fill = self.fills['header_light']
            cell_e2.border = self.borders['thin_all']

            # F2&G2: Pass dies count (Green for good)
            worksheet.merge_cells('F2:G2')
            cell_f2 = worksheet['F2']
            cell_f2.value = f"{total_pass:,}"
            cell_f2.font = self.fonts['yield_good']  # Green color for pass
            cell_f2.alignment = self.alignments['center']
            cell_f2.fill = self.fills['white']
            cell_f2.border = self.borders['thin_all']

            # H2: Yield% (Header)
            cell_h2 = worksheet['H2']
            cell_h2.value = "Yield%"
            cell_h2.font = self.fonts['header_medium']
            cell_h2.alignment = self.alignments['center']
            cell_h2.fill = self.fills['header_light']
            cell_h2.border = self.borders['thin_all']

            # I2: Overall yield percentage (Always green for yield values)
            cell_i2 = worksheet['I2']
            cell_i2.value = f"{overall_yield:.2f}%"
            # Always use green color for yield percentages as requested
            cell_i2.font = self.fonts['yield_good']  # Green color for all yield values
            cell_i2.alignment = self.alignments['center']
            cell_i2.fill = self.fills['white']
            cell_i2.border = self.borders['thin_all']

            # J2: Fail Dice (Header)
            cell_j2 = worksheet['J2']
            cell_j2.value = "Fail Dice"
            cell_j2.font = self.fonts['header_medium']
            cell_j2.alignment = self.alignments['center']
            cell_j2.fill = self.fills['header_light']
            cell_j2.border = self.borders['thin_all']

            # K2&L2: Fail dies count (Red for failures)
            worksheet.merge_cells('K2:L2')
            cell_k2 = worksheet['K2']
            cell_k2.value = f"{total_fail:,}"
            cell_k2.font = self.fonts['fail_count']  # Red color for fail
            cell_k2.alignment = self.alignments['center']
            cell_k2.fill = self.fills['white']
            cell_k2.border = self.borders['thin_all']

        except Exception as e:
            print(f"❌ Error formatting row 2: {e}")

    def _set_optimal_column_widths(self, worksheet):
        """Set optimal column widths for better readability"""
        try:
            # Define optimal widths for each column
            column_widths = {
                'A': 12,   # Device Name (merged with B)
                'B': 12,   # Device Name (merged with A)
                'C': 15,   # Device Value (merged with D)
                'D': 15,   # Device Value (merged with C)
                'E': 10,   # Lot No
                'F': 12,   # Lot Value (merged with G)
                'G': 12,   # Lot Value (merged with F)
                'H': 12,   # Total pcs
                'I': 10,   # Count/Yield value
                'J': 12,   # VENDOR/Fail Dice
                'K': 12,   # Vendor Value (merged with L)
                'L': 12,   # Vendor Value (merged with K)
            }

            for col_letter, width in column_widths.items():
                worksheet.column_dimensions[col_letter].width = width

            # Set row heights for better visual spacing
            worksheet.row_dimensions[1].height = 25  # Row 1 - slightly taller
            worksheet.row_dimensions[2].height = 25  # Row 2 - slightly taller
            worksheet.row_dimensions[3].height = 8   # Row 3 - spacing row
            worksheet.row_dimensions[4].height = 8   # Row 4 - spacing row

        except Exception as e:
            print(f"❌ Error setting column widths: {e}")

    def _add_header_border(self, worksheet):
        """Add complete grid lines and thick black outer border for header area (A1:L2)"""
        try:
            from openpyxl.styles import Border, Side

            # Define border styles
            thick_black_side = Side(border_style='thick', color='000000')  # Outer border
            thin_black_side = Side(border_style='thin', color='000000')   # Inner grid lines

            # Apply complete grid lines to all cells in header area (A1:L2)
            for row in [1, 2]:
                for col in range(1, 13):  # A to L (columns 1-12)
                    cell = worksheet.cell(row=row, column=col)

                    # Start with all thin borders (complete grid)
                    left_border = thin_black_side
                    right_border = thin_black_side
                    top_border = thin_black_side
                    bottom_border = thin_black_side

                    # Override with thick borders for outer edges
                    if col == 1:  # Left edge (column A)
                        left_border = thick_black_side
                    if col == 12:  # Right edge (column L)
                        right_border = thick_black_side
                    if row == 1:  # Top edge (row 1)
                        top_border = thick_black_side
                    if row == 2:  # Bottom edge (row 2)
                        bottom_border = thick_black_side

                    # Apply the combined border
                    cell.border = Border(
                        left=left_border,
                        right=right_border,
                        top=top_border,
                        bottom=bottom_border
                    )

            print("✅ Complete grid lines and thick black outer border added to header area (A1:L2)")

        except Exception as e:
            print(f"❌ Error adding header border: {e}")

    def _format_bin_table_headers(self, worksheet, headers):
        """Format the bin statistics table headers (row 5) with professional styling"""
        try:
            from openpyxl.styles import Font, Alignment, PatternFill, Border, Side

            # Define table header styling
            table_header_font = Font(
                name='Segoe UI',
                size=11,
                bold=True,
                color='FFFFFF'
            )

            table_header_fill = PatternFill(
                start_color='4472C4',  # Professional blue
                end_color='4472C4',
                fill_type='solid'
            )

            table_border = Border(
                left=Side(border_style='thin', color='FFFFFF'),
                right=Side(border_style='thin', color='FFFFFF'),
                top=Side(border_style='medium', color='4472C4'),
                bottom=Side(border_style='medium', color='4472C4')
            )

            center_alignment = Alignment(horizontal='center', vertical='center')

            # Apply formatting to each header cell
            for col_idx, header in enumerate(headers, 1):
                cell = worksheet.cell(row=5, column=col_idx, value=header)
                cell.font = table_header_font
                cell.alignment = center_alignment
                cell.fill = table_header_fill
                cell.border = table_border

                # Special formatting for key columns
                if header == "LotID-waferID":
                    # Make the first column slightly wider
                    from openpyxl.utils import get_column_letter
                    col_letter = get_column_letter(col_idx)
                    worksheet.column_dimensions[col_letter].width = 18
                elif header == "Yield(%)":
                    # Make yield column distinctive
                    cell.fill = PatternFill(
                        start_color='28A745',  # Green for yield
                        end_color='28A745',
                        fill_type='solid'
                    )
                elif header.startswith('C'):
                    # Bin columns - slightly narrower
                    from openpyxl.utils import get_column_letter
                    col_letter = get_column_letter(col_idx)
                    worksheet.column_dimensions[col_letter].width = 6

            # Set row height for table header
            worksheet.row_dimensions[5].height = 22

        except Exception as e:
            print(f"❌ Error formatting bin table headers: {e}")

    def _format_data_rows(self, worksheet, start_row, end_row):
        """Format data rows with alternating colors for better readability"""
        try:
            from openpyxl.styles import Font, Alignment, PatternFill, Border, Side

            # Define data row styling
            data_font = Font(name='Calibri', size=10, color='333333')
            center_alignment = Alignment(horizontal='center', vertical='center')
            left_alignment = Alignment(horizontal='left', vertical='center')

            # Alternating row colors
            even_row_fill = PatternFill(
                start_color='F8F9FA',  # Very light gray
                end_color='F8F9FA',
                fill_type='solid'
            )

            odd_row_fill = PatternFill(
                start_color='FFFFFF',  # White
                end_color='FFFFFF',
                fill_type='solid'
            )

            thin_border = Border(
                left=Side(border_style='thin', color='E0E0E0'),
                right=Side(border_style='thin', color='E0E0E0'),
                top=Side(border_style='thin', color='E0E0E0'),
                bottom=Side(border_style='thin', color='E0E0E0')
            )

            # Apply formatting to data rows
            for row_idx in range(start_row, end_row + 1):
                is_even_row = (row_idx - start_row) % 2 == 0
                row_fill = even_row_fill if is_even_row else odd_row_fill

                # Get the maximum column with data
                max_col = worksheet.max_column

                for col_idx in range(1, max_col + 1):
                    cell = worksheet.cell(row=row_idx, column=col_idx)
                    cell.font = data_font
                    cell.fill = row_fill
                    cell.border = thin_border

                    # Special alignment for different columns
                    if col_idx == 1:  # LotID-waferID column
                        cell.alignment = left_alignment
                    else:  # All other columns
                        cell.alignment = center_alignment

                        # Special formatting for yield column (column 2) - Always green
                        if col_idx == 2 and cell.value:
                            # Always use green color for all yield percentages as requested
                            cell.font = Font(name='Calibri', size=10, bold=True, color='28A745')  # Green for all yield values

                # Set row height
                worksheet.row_dimensions[row_idx].height = 18

        except Exception as e:
            print(f"❌ Error formatting data rows: {e}")

    def _format_average_row(self, worksheet, avg_row):
        """Format the average row with special highlighting"""
        try:
            from openpyxl.styles import Font, Alignment, PatternFill, Border, Side

            # Define average row styling
            avg_font = Font(name='Calibri', size=11, bold=True, color='FFFFFF')
            # Special green font for yield column (column 2)
            yield_font = Font(name='Calibri', size=11, bold=True, color='28A745')  # Green for yield
            avg_fill = PatternFill(
                start_color='6C757D',  # Gray background for average
                end_color='6C757D',
                fill_type='solid'
            )

            avg_border = Border(
                left=Side(border_style='medium', color='6C757D'),
                right=Side(border_style='medium', color='6C757D'),
                top=Side(border_style='medium', color='6C757D'),
                bottom=Side(border_style='medium', color='6C757D')
            )

            center_alignment = Alignment(horizontal='center', vertical='center')
            left_alignment = Alignment(horizontal='left', vertical='center')

            # Get the maximum column with data
            max_col = worksheet.max_column

            # Apply formatting to average row
            for col_idx in range(1, max_col + 1):
                cell = worksheet.cell(row=avg_row, column=col_idx)

                # Special handling for yield column (column 2) - use green font
                if col_idx == 2:
                    cell.font = yield_font  # Green font for yield percentage
                else:
                    cell.font = avg_font    # White font for other columns

                cell.fill = avg_fill
                cell.border = avg_border

                if col_idx == 1:  # "Average" label
                    cell.alignment = left_alignment
                else:
                    cell.alignment = center_alignment

            # Set row height for average row
            worksheet.row_dimensions[avg_row].height = 22

        except Exception as e:
            print(f"❌ Error formatting average row: {e}")


def main():
    """Main function for testing"""
    import sys
    
    if len(sys.argv) < 2:
        print("Full Map Processor Test")
        print("Usage: python full_map_processor.py <map_file1> [map_file2] ...")
        return
    
    file_paths = sys.argv[1:]
    
    processor = FullMapProcessor()
    processor.set_rotation_angle(0)
    processor.set_filter_empty(True)
    
    # Test with config file if available
    config_path = "3509_CP1_program_bin.xlsx"
    if os.path.exists(config_path):
        config_reader = ConfigReader()
        if config_reader.read_config_file(config_path):
            processor.set_config_reader(config_reader)
            print(f"✅ Configuration loaded: {len(config_reader.get_all_bin_mappings())} bin mappings")
    
    output_file = processor.process_multiple_files(file_paths)
    
    if output_file:
        print(f"🎉 Success! Output file: {output_file}")
    else:
        print("❌ Processing failed")


if __name__ == "__main__":
    main()
