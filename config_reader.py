#!/usr/bin/env python3
"""
Configuration File Reader
Reads Excel configuration files for bin name mapping and test program info
"""

import os
from typing import Dict, Optional
try:
    from openpyxl import load_workbook
except ImportError:
    print("Error: openpyxl not installed. Please run: pip install openpyxl")
    exit(1)


class ConfigReader:
    """Configuration file reader for Excel files"""
    
    def __init__(self):
        self.test_program_name = ""
        self.bin_name_mapping = {}  # {bin_number: bin_name}
        self.device_name = ""  # D2 cell content
        self.vendor_name = ""  # F2 cell content
    
    def read_config_file(self, config_file_path: str) -> bool:
        """
        Read configuration Excel file
        Returns True if successful, False otherwise
        """
        if not os.path.exists(config_file_path):
            print(f"Configuration file not found: {config_file_path}")
            return False
        
        try:
            # Load Excel workbook
            workbook = load_workbook(config_file_path, read_only=True)
            
            # Use the first worksheet
            worksheet = workbook.active
            
            # Read A2 cell for test program name
            self.test_program_name = self._get_cell_value(worksheet, 'A2')

            # Read D2 cell for device name
            self.device_name = self._get_cell_value(worksheet, 'D2')

            # Read F2 cell for vendor name
            self.vendor_name = self._get_cell_value(worksheet, 'F2')

            # Read bin name mapping from A6~AXX and B6~BXX
            self.bin_name_mapping = self._read_bin_mapping(worksheet)
            
            workbook.close()
            
            print(f"Configuration loaded: {len(self.bin_name_mapping)} bin mappings")
            return True
            
        except Exception as e:
            print(f"Error reading configuration file: {e}")
            return False
    
    def _get_cell_value(self, worksheet, cell_address: str) -> str:
        """Get cell value as string"""
        try:
            cell = worksheet[cell_address]
            if cell.value is None:
                return ""
            return str(cell.value).strip()
        except:
            return ""
    
    def _read_bin_mapping(self, worksheet) -> Dict[int, str]:
        """
        Read bin mapping from columns A and B starting from row 6
        A column: bin numbers, B column: bin names
        """
        bin_mapping = {}
        
        try:
            # Start from row 6 and read until empty cells
            row = 6
            max_rows = worksheet.max_row if worksheet.max_row else 1000
            
            while row <= max_rows:
                # Read A column (bin number)
                bin_cell = worksheet.cell(row=row, column=1)  # Column A
                name_cell = worksheet.cell(row=row, column=2)  # Column B
                
                # Check if both cells have values
                if bin_cell.value is None and name_cell.value is None:
                    # If both are empty, we might have reached the end
                    # But continue for a few more rows in case there are gaps
                    empty_count = 0
                    for check_row in range(row, min(row + 10, max_rows + 1)):
                        check_bin = worksheet.cell(row=check_row, column=1).value
                        check_name = worksheet.cell(row=check_row, column=2).value
                        if check_bin is None and check_name is None:
                            empty_count += 1
                        else:
                            break
                    
                    if empty_count >= 5:  # 5 consecutive empty rows, assume end
                        break
                
                # Process the current row if it has data
                if bin_cell.value is not None or name_cell.value is not None:
                    try:
                        # Try to convert bin number
                        if bin_cell.value is not None:
                            bin_number = int(float(str(bin_cell.value)))
                            bin_name = str(name_cell.value).strip() if name_cell.value else ""
                            
                            if bin_name:  # Only add if name is not empty
                                bin_mapping[bin_number] = bin_name
                    except (ValueError, TypeError):
                        # Skip invalid bin numbers
                        pass
                
                row += 1
            
        except Exception as e:
            print(f"Error reading bin mapping: {e}")
        
        return bin_mapping
    
    def get_test_program_name(self) -> str:
        """Get test program name from A2"""
        return self.test_program_name

    def get_device_name(self) -> str:
        """Get device name from D2"""
        return self.device_name

    def get_vendor_name(self) -> str:
        """Get vendor name from F2"""
        return self.vendor_name

    def get_bin_name(self, bin_number: int) -> str:
        """Get bin name for given bin number"""
        return self.bin_name_mapping.get(bin_number, "")
    
    def get_formatted_bin_name(self, bin_number: int) -> str:
        """
        Get formatted bin name in format "数字(名称)"
        If no name mapping exists, return just "数字"
        """
        bin_name = self.get_bin_name(bin_number)
        if bin_name:
            return f"{bin_number}({bin_name})"
        else:
            return f"{bin_number}"
    
    def get_all_bin_mappings(self) -> Dict[int, str]:
        """Get all bin mappings"""
        return self.bin_name_mapping.copy()
    
    def has_config_loaded(self) -> bool:
        """Check if configuration has been loaded"""
        return bool(self.test_program_name or self.bin_name_mapping)


def test_config_reader():
    """Test function for ConfigReader"""
    print("Testing ConfigReader...")
    
    reader = ConfigReader()
    
    # Test with a sample file (you would need to create this)
    test_file = "test_config.xlsx"
    if os.path.exists(test_file):
        if reader.read_config_file(test_file):
            print(f"Test Program: '{reader.get_test_program_name()}'")
            print(f"Bin mappings: {reader.get_all_bin_mappings()}")
            
            # Test formatted names
            for bin_num in [1, 2, 5, 999]:
                formatted = reader.get_formatted_bin_name(bin_num)
                print(f"Bin {bin_num}: {formatted}")
        else:
            print("Failed to read test config file")
    else:
        print(f"Test config file not found: {test_file}")


if __name__ == "__main__":
    test_config_reader()
