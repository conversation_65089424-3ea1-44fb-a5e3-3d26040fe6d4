#!/usr/bin/env python3
"""
Test script for output folder selection functionality
Tests both AB Map Tool and Full Map Tool output folder features
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# Add parent directory to path to import modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tsk_map_gui import ABMapGUI
from full_map_processor import FullMapProcessor
from tsk_map_processor import TSKMapProcessor


def test_ab_map_tool_output_folder():
    """Test AB Map Tool output folder functionality"""
    print("🧪 Testing AB Map Tool Output Folder Selection...")
    
    # Create temporary output folder
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"📁 Using temporary output folder: {temp_dir}")
        
        # Test file path (use existing test file)
        test_file = "test/3AC468-01-C5.map"
        if not os.path.exists(test_file):
            print(f"❌ Test file not found: {test_file}")
            return False
        
        try:
            # Create processor
            processor = TSKMapProcessor()
            
            # Read and process test file
            if not processor.read_file(test_file):
                print("❌ Failed to read test file")
                return False
            
            if not processor.parse_file_header():
                print("❌ Failed to parse file header")
                return False
            
            if not processor.process_die_data():
                print("❌ Failed to process die data")
                return False
            
            # Test output filename generation with folder
            base_name = os.path.splitext(os.path.basename(test_file))[0]
            rotation = 0
            filename = f"{base_name}_Amap_R{rotation}.xlsx"
            
            # Simulate output folder selection
            output_filename = os.path.join(temp_dir, filename)
            
            # Create a simple Excel file to test
            from openpyxl import Workbook
            wb = Workbook()
            ws = wb.active
            ws.title = "Test"
            ws['A1'] = "Test Output"
            wb.save(output_filename)
            wb.close()
            
            # Verify file was created in correct location
            if os.path.exists(output_filename):
                print(f"✅ File created successfully: {output_filename}")
                file_size = os.path.getsize(output_filename)
                print(f"   File size: {file_size} bytes")
                return True
            else:
                print("❌ File was not created in specified folder")
                return False
                
        except Exception as e:
            print(f"❌ Error testing AB Map Tool: {e}")
            return False


def test_full_map_tool_output_folder():
    """Test Full Map Tool output folder functionality"""
    print("\n🧪 Testing Full Map Tool Output Folder Selection...")
    
    # Create temporary output folder
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"📁 Using temporary output folder: {temp_dir}")
        
        # Test file path
        test_file = "test/3AC468-01-C5.map"
        if not os.path.exists(test_file):
            print(f"❌ Test file not found: {test_file}")
            return False
        
        try:
            # Create Full Map Processor
            processor = FullMapProcessor()
            processor.set_rotation_angle(0)
            processor.set_filter_empty(True)
            processor.set_output_folder(temp_dir)
            
            # Process single file
            result = processor.process_multiple_files([test_file])
            
            if result:
                print(f"✅ File created successfully: {result}")
                
                # Verify file is in correct location
                if result.startswith(temp_dir):
                    print("✅ File created in specified output folder")
                    
                    # Check if file exists and has reasonable size
                    if os.path.exists(result):
                        file_size = os.path.getsize(result)
                        print(f"   File size: {file_size} bytes")
                        
                        # Verify Bin_Summary sheet exists
                        from openpyxl import load_workbook
                        wb = load_workbook(result)
                        sheet_names = wb.sheetnames
                        
                        if "Bin_Summary" in sheet_names and sheet_names[0] == "Bin_Summary":
                            print("✅ Bin_Summary sheet created as first sheet")
                        else:
                            print(f"❌ Bin_Summary sheet not found or not first. Sheets: {sheet_names}")
                        
                        wb.close()
                        return True
                    else:
                        print("❌ Generated file does not exist")
                        return False
                else:
                    print(f"❌ File not created in specified folder. Path: {result}")
                    return False
            else:
                print("❌ Failed to process file")
                return False
                
        except Exception as e:
            print(f"❌ Error testing Full Map Tool: {e}")
            return False


def test_bin_summary_functionality():
    """Test Bin_Summary sheet functionality"""
    print("\n🧪 Testing Bin_Summary Sheet Functionality...")
    
    test_file = "test/3AC468-01-C5.map"
    if not os.path.exists(test_file):
        print(f"❌ Test file not found: {test_file}")
        return False
    
    try:
        # Create Full Map Processor
        processor = FullMapProcessor()
        processor.set_rotation_angle(0)
        processor.set_filter_empty(True)
        
        # Process file
        result = processor.process_multiple_files([test_file])
        
        if result and os.path.exists(result):
            print(f"✅ Excel file created: {result}")
            
            # Load and examine the Excel file
            from openpyxl import load_workbook
            wb = load_workbook(result)
            
            # Check if Bin_Summary is the first sheet
            if wb.sheetnames[0] == "Bin_Summary":
                print("✅ Bin_Summary is the first sheet")
                
                # Examine Bin_Summary sheet structure
                ws = wb["Bin_Summary"]
                
                # Check headers in row 5
                headers_row = 5
                expected_headers = ["LotID-waferID", "Yield(%)"]
                
                # Check first two headers
                header1 = ws.cell(row=headers_row, column=1).value
                header2 = ws.cell(row=headers_row, column=2).value
                
                if header1 == expected_headers[0] and header2 == expected_headers[1]:
                    print("✅ Headers are correct in row 5")
                    
                    # Check bin columns (C00, C01, etc.)
                    c00_header = ws.cell(row=headers_row, column=3).value
                    c01_header = ws.cell(row=headers_row, column=4).value
                    
                    if c00_header == "C00" and c01_header == "C01":
                        print("✅ Bin columns are correctly formatted")
                        
                        # Check if data exists in row 6
                        sheet_name = ws.cell(row=6, column=1).value
                        yield_value = ws.cell(row=6, column=2).value
                        
                        if sheet_name and yield_value is not None:
                            print(f"✅ Data found - Sheet: {sheet_name}, Yield: {yield_value}")
                            
                            # Look for Average row
                            for row in range(7, 15):  # Check a few rows after data
                                if ws.cell(row=row, column=1).value == "Average":
                                    print(f"✅ Average row found at row {row}")
                                    avg_yield = ws.cell(row=row, column=2).value
                                    print(f"   Average yield: {avg_yield}")
                                    wb.close()
                                    return True
                            
                            print("⚠️ Average row not found")
                            wb.close()
                            return True  # Still consider success if data is there
                        else:
                            print("❌ No data found in row 6")
                    else:
                        print(f"❌ Bin columns incorrect: {c00_header}, {c01_header}")
                else:
                    print(f"❌ Headers incorrect: {header1}, {header2}")
            else:
                print(f"❌ Bin_Summary is not the first sheet. First sheet: {wb.sheetnames[0]}")
            
            wb.close()
            return False
            
        else:
            print("❌ Failed to create Excel file")
            return False
            
    except Exception as e:
        print(f"❌ Error testing Bin_Summary functionality: {e}")
        return False


def main():
    """Run all tests"""
    print("🚀 Starting Output Folder Selection and Bin_Summary Tests")
    print("=" * 60)
    
    # Run tests
    test1_result = test_ab_map_tool_output_folder()
    test2_result = test_full_map_tool_output_folder()
    test3_result = test_bin_summary_functionality()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print(f"   AB Map Tool Output Folder: {'✅ PASS' if test1_result else '❌ FAIL'}")
    print(f"   Full Map Tool Output Folder: {'✅ PASS' if test2_result else '❌ FAIL'}")
    print(f"   Bin_Summary Functionality: {'✅ PASS' if test3_result else '❌ FAIL'}")
    
    all_passed = test1_result and test2_result and test3_result
    print(f"\n🎯 Overall Result: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
