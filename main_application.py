#!/usr/bin/env python3
"""
Main Application Controller - TSK/MAP File Processor Tool
Manages switching between Tool Selector, AB Map Tool, and Full Map Tool
Author: Yuribytes
Company: Chipone TE development Team
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os


class TSKMapApplication:
    """Main application controller for TSK/MAP File Processor Tool"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("TSK/MAP File Processor Tool")
        self.root.geometry("800x700")
        self.root.resizable(True, True)
        
        # Configure ttk style
        self.setup_styles()
        
        # Tool instances
        self.tool_selector = None
        self.ab_map_tool = None
        self.full_map_tool = None
        self.current_tool = "selector"
        
        # Configure grid weights for main window
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        
        # Set up window close protocol
        self.root.protocol("WM_DELETE_WINDOW", self.on_window_close)
        
        # Center window
        self.center_window()
        
        # Start with tool selector
        self.show_tool_selector()
    
    def setup_styles(self):
        """Configure ttk styles for consistent appearance"""
        style = ttk.Style()
        style.theme_use('clam')  # Use a modern theme
        
        # Configure custom styles
        style.configure("Accent.TButton", font=("Arial", 10, "bold"))
        style.configure("Secondary.TButton", font=("Arial", 9))
    
    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def show_tool_selector(self):
        """显示工具选择界面"""
        print("Switching to Tool Selector")
        self.hide_all_tools()
        
        if not self.tool_selector:
            from tool_selector_frame import ToolSelectorFrame
            self.tool_selector = ToolSelectorFrame(self.root, self)
        
        self.tool_selector.show()
        self.current_tool = "selector"
        
        # Adjust window size for selector
        self.root.geometry("800x700")
        self.root.title("TSK/MAP File Processor Tool - Select Tool")
    
    def show_ab_map_tool(self):
        """显示AB Map工具"""
        print("Switching to AB Map Tool")
        self.hide_all_tools()
        
        if not self.ab_map_tool:
            from ab_map_tool_frame import ABMapToolFrame
            self.ab_map_tool = ABMapToolFrame(self.root, self)
        
        self.ab_map_tool.show()
        self.current_tool = "ab_map"
        
        # Adjust window size for AB Map tool (increased height to avoid overlap)
        self.root.geometry("900x900")
        self.root.title("TSK/MAP File Processor Tool - AB Map Tool")
    
    def show_full_map_tool(self):
        """显示Full Map工具"""
        print("Switching to Full Map Tool")
        self.hide_all_tools()
        
        if not self.full_map_tool:
            from full_map_tool_frame import FullMapToolFrame
            self.full_map_tool = FullMapToolFrame(self.root, self)
        
        self.full_map_tool.show()
        self.current_tool = "full_map"
        
        # Adjust window size for Full Map tool (increased height to avoid content clipping)
        self.root.geometry("800x920")
        self.root.title("TSK/MAP File Processor Tool - Full Map Tool")
    
    def hide_all_tools(self):
        """隐藏所有工具界面"""
        if self.tool_selector:
            self.tool_selector.hide()
        if self.ab_map_tool:
            self.ab_map_tool.hide()
        if self.full_map_tool:
            self.full_map_tool.hide()
    
    def return_to_selector_with_confirmation(self, tool_name, has_unsaved_work=False):
        """带确认的返回选择器（供工具调用）"""
        if has_unsaved_work:
            result = messagebox.askyesnocancel(
                "Return to Main Menu",
                f"You have active data in {tool_name}.\n"
                "Clear memory before returning to main menu?",
                icon='question'
            )
            
            if result is None:  # Cancel
                return False
            elif result:  # Yes - clear memory
                if self.current_tool == "ab_map" and self.ab_map_tool:
                    self.ab_map_tool.clear_memory()
                elif self.current_tool == "full_map" and self.full_map_tool:
                    self.full_map_tool.clear_memory()
        
        self.show_tool_selector()
        return True
    
    def on_window_close(self):
        """Handle window close event"""
        # Always check for memory and clear it before exit
        memory_before_clear = 0.0
        processor_count = 0
        has_unsaved = False

        # Get memory info from all tools before clearing
        if self.ab_map_tool and hasattr(self.ab_map_tool, 'ab_map_gui'):
            if self.ab_map_tool.ab_map_gui.amap_processor:
                if hasattr(self.ab_map_tool.ab_map_gui.amap_processor, 'get_memory_usage_mb'):
                    memory_before_clear += self.ab_map_tool.ab_map_gui.amap_processor.get_memory_usage_mb()
                processor_count += 1
            if self.ab_map_tool.ab_map_gui.bmap_processor:
                if hasattr(self.ab_map_tool.ab_map_gui.bmap_processor, 'get_memory_usage_mb'):
                    memory_before_clear += self.ab_map_tool.ab_map_gui.bmap_processor.get_memory_usage_mb()
                processor_count += 1
            has_unsaved = has_unsaved or self.ab_map_tool.has_unsaved_work()

        if self.full_map_tool and hasattr(self.full_map_tool, 'processors'):
            for processor in self.full_map_tool.processors.values():
                if hasattr(processor, 'get_memory_usage_mb'):
                    memory_before_clear += processor.get_memory_usage_mb()
                    processor_count += 1
            has_unsaved = has_unsaved or self.full_map_tool.has_unsaved_work()

        # Ask for confirmation only if there's unsaved work
        if has_unsaved:
            result = messagebox.askyesnocancel(
                "Exit Application",
                "You have unsaved work. Clear memory before exiting?",
                icon='question'
            )

            if result is None:  # Cancel
                return
            elif result:  # Yes - clear memory
                if self.ab_map_tool:
                    self.ab_map_tool.clear_memory()
                if self.full_map_tool:
                    self.full_map_tool.clear_memory()
        else:
            # No unsaved work, but still clear memory
            if self.ab_map_tool:
                self.ab_map_tool.clear_memory()
            if self.full_map_tool:
                self.full_map_tool.clear_memory()

        # Verify memory was actually cleared
        memory_after_clear = 0.0
        if self.ab_map_tool and hasattr(self.ab_map_tool, 'ab_map_gui'):
            if self.ab_map_tool.ab_map_gui.amap_processor:
                if hasattr(self.ab_map_tool.ab_map_gui.amap_processor, 'get_memory_usage_mb'):
                    memory_after_clear += self.ab_map_tool.ab_map_gui.amap_processor.get_memory_usage_mb()
            if self.ab_map_tool.ab_map_gui.bmap_processor:
                if hasattr(self.ab_map_tool.ab_map_gui.bmap_processor, 'get_memory_usage_mb'):
                    memory_after_clear += self.ab_map_tool.ab_map_gui.bmap_processor.get_memory_usage_mb()

        if self.full_map_tool and hasattr(self.full_map_tool, 'processors'):
            for processor in self.full_map_tool.processors.values():
                if hasattr(processor, 'get_memory_usage_mb'):
                    memory_after_clear += processor.get_memory_usage_mb()

        # Calculate actual memory freed
        actual_memory_freed = memory_before_clear - memory_after_clear
        print(f"Main Application - Actual memory freed on exit: {actual_memory_freed:.1f} MB (before: {memory_before_clear:.1f} MB, after: {memory_after_clear:.1f} MB)")

        # Always show cleanup popup when exiting with actual freed memory
        self.show_exit_cleanup_popup(actual_memory_freed, processor_count)
        self.root.after(2100, self.root.quit)

    def show_exit_cleanup_popup(self, memory_freed: float, processor_count: int):
        """Show exit cleanup popup"""
        try:
            # Create popup window
            popup = tk.Toplevel(self.root)
            popup.title("Memory Cleanup")
            popup.geometry("350x120")
            popup.resizable(False, False)

            # Center popup on parent window
            popup.transient(self.root)
            popup.grab_set()

            # Center popup
            x = self.root.winfo_x() + (self.root.winfo_width() // 2) - 175
            y = self.root.winfo_y() + (self.root.winfo_height() // 2) - 60
            popup.geometry(f"350x120+{x}+{y}")

            # Create content
            main_frame = ttk.Frame(popup, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # Icon and message
            if memory_freed > 0:
                message = f"✅ Memory Cleanup Complete\n\n{memory_freed:.1f} MB freed from {processor_count} processors"
            elif processor_count > 0:
                message = f"✅ Memory Cleanup Complete\n\nProcessors cleared (no memory to free)"
            else:
                message = "✅ Application Closing\n\nThank you for using TSK/MAP File Processor Tool!"

            ttk.Label(main_frame, text=message, justify=tk.CENTER,
                     font=("Arial", 10)).pack(expand=True)

            # Auto-close after 2 seconds
            popup.after(2000, popup.destroy)

            # Allow manual close with Escape key
            popup.bind('<Escape>', lambda e: popup.destroy())
            popup.focus_set()

        except Exception as e:
            print(f"Warning: Error showing exit cleanup popup: {e}")
    
    def run(self):
        """启动应用程序"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            print("\nApplication interrupted by user")
        except Exception as e:
            print(f"Application error: {e}")
            messagebox.showerror("Application Error", f"An error occurred:\n{str(e)}")
        finally:
            try:
                self.root.quit()
            except:
                pass


def main():
    """Main function"""
    try:
        app = TSKMapApplication()
        app.run()
    except Exception as e:
        print(f"Failed to start application: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
