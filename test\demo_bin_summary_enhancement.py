#!/usr/bin/env python3
"""
Demo script for the enhanced Bin_Summary sheet functionality
Shows the new header information and summary statistics
"""

import os
import sys
import tempfile

# Add parent directory to path to import modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def demo_enhanced_bin_summary():
    """Demonstrate the enhanced Bin_Summary sheet functionality"""
    print("🎯 Enhanced Bin_Summary Sheet Demo")
    print("=" * 60)
    
    try:
        from full_map_processor import FullMapProcessor
        from config_reader import ConfigReader
        from openpyxl import Workbook
        
        print("📋 New Bin_Summary Sheet Structure")
        print("=" * 50)
        
        print("🔹 Row 1 - Device and Lot Information:")
        print("   A1&B1: Device Name    | C1&D1: [From config D2 cell]")
        print("   E1: Lot No           | F1&G1: [From first MAP filename]")
        print("   H1: Total pcs        | I1: [Number of MAP files]")
        print("   J1: VENDOR           | K1&L1: [From config F2 cell]")
        
        print("\n🔹 Row 2 - Test Statistics:")
        print("   A2&B2: Total Tested  | C2&D2: [Sum of all tested dies]")
        print("   E2: Pass Dice        | F2&G2: [Sum of all pass dies]")
        print("   H2: Yield%           | I2: [Overall yield percentage]")
        print("   J2: Fail Dice        | K2&L2: [Total - Pass dies]")
        
        print("\n🔹 Row 5+: Original bin statistics table (unchanged)")
        
        # Create demo with mock data
        print("\n" + "=" * 60)
        print("📊 Demo with Sample Data")
        print("=" * 60)
        
        # Create mock config
        config_reader = ConfigReader()
        config_reader.device_name = "SampleChip_V2.1"
        config_reader.vendor_name = "ChipOne_Technology"
        config_reader.test_program_name = "TestProg_Final_V1.0"
        
        # Create processor
        processor = FullMapProcessor()
        processor.set_config_reader(config_reader)
        
        # Create mock processors data
        mock_processors = {
            "LOT2024_001-W01-Site1.map": {
                "processor": MockTSKProcessor(2500, 2350),  # 2500 tested, 2350 pass (94% yield)
                "file_path": "LOT2024_001-W01-Site1.map"
            },
            "LOT2024_001-W02-Site1.map": {
                "processor": MockTSKProcessor(2480, 2290),  # 2480 tested, 2290 pass (92.3% yield)
                "file_path": "LOT2024_001-W02-Site1.map"
            },
            "LOT2024_001-W03-Site1.map": {
                "processor": MockTSKProcessor(2520, 2400),  # 2520 tested, 2400 pass (95.2% yield)
                "file_path": "LOT2024_001-W03-Site1.map"
            }
        }
        
        # Create test workbook
        workbook = Workbook()
        worksheet = workbook.active
        worksheet.title = "Bin_Summary"
        
        print("🔄 Processing sample data...")
        processor._create_summary_header(worksheet, mock_processors)
        
        # Display results
        print("\n📋 Generated Header Information:")
        print("=" * 50)
        
        # Row 1 information
        device_name = worksheet['C1'].value
        lot_no = worksheet['F1'].value
        total_maps = worksheet['I1'].value
        vendor = worksheet['K1'].value
        
        print(f"🔹 Device Name: {device_name}")
        print(f"🔹 Lot Number: {lot_no}")
        print(f"🔹 Total MAP files: {total_maps}")
        print(f"🔹 Vendor: {vendor}")
        
        # Row 2 statistics
        total_tested = worksheet['C2'].value
        pass_dice = worksheet['F2'].value
        yield_pct = worksheet['I2'].value
        fail_dice = worksheet['K2'].value
        
        print(f"\n📊 Test Statistics:")
        print(f"🔹 Total Tested Dies: {total_tested:,}")
        print(f"🔹 Pass Dies: {pass_dice:,}")
        print(f"🔹 Fail Dies: {fail_dice:,}")
        print(f"🔹 Overall Yield: {yield_pct}")
        
        # Calculate individual yields for comparison
        print(f"\n📈 Individual MAP Yields:")
        for filename, data in mock_processors.items():
            stats = data['processor'].get_test_statistics()
            wafer_id = filename.split('-')[1] if '-' in filename else filename
            print(f"🔹 {wafer_id}: {stats['yield_percentage']:.2f}% ({stats['pass_count']}/{stats['total_tested']})")
        
        # Show the benefit
        print(f"\n💡 Benefits of Enhanced Header:")
        print("=" * 50)
        print("✅ Quick overview without scrolling through data")
        print("✅ Device and vendor information from config file")
        print("✅ Automatic lot number extraction from filenames")
        print("✅ Consolidated statistics across all MAP files")
        print("✅ Easy yield comparison and analysis")
        print("✅ Professional report format")
        
        workbook.close()
        return True
        
    except Exception as e:
        print(f"❌ Error during demo: {e}")
        import traceback
        traceback.print_exc()
        return False


def demo_config_integration():
    """Demonstrate config file integration"""
    print("\n" + "=" * 60)
    print("🎯 Config File Integration Demo")
    print("=" * 60)
    
    try:
        from config_reader import ConfigReader
        from openpyxl import Workbook
        
        # Create sample config file
        config_path = os.path.join(tempfile.gettempdir(), "demo_config.xlsx")
        
        print("📝 Creating sample config file...")
        wb = Workbook()
        ws = wb.active
        
        # Set sample data
        ws['A2'] = "ProductionTest_V3.2"
        ws['D2'] = "AdvancedProcessor_AP2024"
        ws['F2'] = "TechCorp_Manufacturing"
        
        # Add bin mappings
        ws['A6'] = 0
        ws['B6'] = "PASS"
        ws['A7'] = 1
        ws['B7'] = "FAIL_CONTACT"
        ws['A8'] = 2
        ws['B8'] = "FAIL_LEAKAGE"
        ws['A9'] = 3
        ws['B9'] = "FAIL_SPEED"
        
        wb.save(config_path)
        wb.close()
        
        print(f"✅ Config file created: {os.path.basename(config_path)}")
        
        # Test reading
        config_reader = ConfigReader()
        if config_reader.read_config_file(config_path):
            print("✅ Config file loaded successfully")
            
            print(f"\n📋 Extracted Information:")
            print(f"🔹 Test Program (A2): {config_reader.get_test_program_name()}")
            print(f"🔹 Device Name (D2): {config_reader.get_device_name()}")
            print(f"🔹 Vendor Name (F2): {config_reader.get_vendor_name()}")
            
            print(f"\n🏷️  Bin Mappings:")
            for bin_num, bin_name in config_reader.bin_name_mapping.items():
                print(f"🔹 bin{bin_num}: {bin_name}")
            
            print(f"\n💡 Usage in Bin_Summary:")
            print("=" * 40)
            print("• Device Name → C1&D1 merged cells")
            print("• Vendor Name → K1&L1 merged cells")
            print("• Bin mappings → Used in individual MAP sheets")
            print("• Test Program → Available for future use")
            
        else:
            print("❌ Failed to load config file")
            return False
        
        # Cleanup
        os.remove(config_path)
        return True
        
    except Exception as e:
        print(f"❌ Error during config demo: {e}")
        return False


def demo_calculation_logic():
    """Demonstrate the calculation logic"""
    print("\n" + "=" * 60)
    print("🎯 Calculation Logic Demo")
    print("=" * 60)
    
    print("📊 How Statistics are Calculated:")
    print("=" * 40)
    
    # Sample data
    maps_data = [
        {"name": "Wafer01", "tested": 1000, "pass": 950},
        {"name": "Wafer02", "tested": 1200, "pass": 1140},
        {"name": "Wafer03", "tested": 980, "pass": 931},
    ]
    
    print("📋 Sample MAP Files:")
    total_tested = 0
    total_pass = 0
    
    for i, data in enumerate(maps_data, 1):
        tested = data["tested"]
        passed = data["pass"]
        yield_pct = (passed / tested * 100) if tested > 0 else 0
        
        print(f"   {i}. {data['name']}: {tested:,} tested, {passed:,} pass ({yield_pct:.2f}%)")
        
        total_tested += tested
        total_pass += passed
    
    total_fail = total_tested - total_pass
    overall_yield = (total_pass / total_tested * 100) if total_tested > 0 else 0
    
    print(f"\n🧮 Calculations:")
    print(f"   Total Tested = {maps_data[0]['tested']} + {maps_data[1]['tested']} + {maps_data[2]['tested']} = {total_tested:,}")
    print(f"   Total Pass = {maps_data[0]['pass']} + {maps_data[1]['pass']} + {maps_data[2]['pass']} = {total_pass:,}")
    print(f"   Total Fail = {total_tested:,} - {total_pass:,} = {total_fail:,}")
    print(f"   Overall Yield = ({total_pass:,} / {total_tested:,}) × 100 = {overall_yield:.2f}%")
    
    print(f"\n📍 Where These Values Appear in Bin_Summary:")
    print(f"   C2&D2 (Total Tested): {total_tested:,}")
    print(f"   F2&G2 (Pass Dice): {total_pass:,}")
    print(f"   I2 (Yield%): {overall_yield:.2f}%")
    print(f"   K2&L2 (Fail Dice): {total_fail:,}")
    print(f"   I1 (Total pcs): {len(maps_data)} MAP files")
    
    return True


class MockTSKProcessor:
    """Mock TSKMapProcessor for demo"""
    def __init__(self, total_tested, pass_count):
        self.total_tested = total_tested
        self.pass_count = pass_count
    
    def get_test_statistics(self):
        return {
            'total_tested': self.total_tested,
            'pass_count': self.pass_count,
            'yield_percentage': (self.pass_count / self.total_tested * 100) if self.total_tested > 0 else 0.0
        }


def main():
    """Run the complete demo"""
    print("🎉 BIN_SUMMARY ENHANCEMENT DEMO")
    print("=" * 80)
    print("This demo shows the new enhanced Bin_Summary sheet functionality")
    print("=" * 80)
    
    # Run demos
    demo1 = demo_enhanced_bin_summary()
    demo2 = demo_config_integration()
    demo3 = demo_calculation_logic()
    
    # Summary
    print("\n" + "=" * 80)
    print("📋 DEMO SUMMARY")
    print("=" * 80)
    
    if demo1 and demo2 and demo3:
        print("🎉 ALL DEMOS COMPLETED SUCCESSFULLY!")
        
        print("\n🎯 NEW FEATURES SUMMARY:")
        print("   ✅ Enhanced Bin_Summary header with device and lot information")
        print("   ✅ Automatic statistics calculation across all MAP files")
        print("   ✅ Config file integration (D2, F2 cells)")
        print("   ✅ Professional report format with merged cells")
        print("   ✅ Maintains all existing bin statistics functionality")
        
        print("\n👥 USER BENEFITS:")
        print("   • Quick overview of test results without scrolling")
        print("   • Device and vendor information prominently displayed")
        print("   • Automatic lot number extraction from filenames")
        print("   • Consolidated yield analysis across multiple wafers")
        print("   • Professional Excel report format")
        
        print("\n🔧 TECHNICAL BENEFITS:")
        print("   • Backward compatible with existing functionality")
        print("   • Automatic calculation - no manual input required")
        print("   • Flexible config file integration")
        print("   • Clean, maintainable code structure")
        
    else:
        print("❌ Some demos failed. Please check the implementation.")
    
    return demo1 and demo2 and demo3


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
