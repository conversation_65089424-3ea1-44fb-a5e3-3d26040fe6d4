#!/usr/bin/env python3
"""
Test script to verify space optimization for Full Map Tool
Tests scrollbar functionality and compact layout
"""

import os
import sys
import tkinter as tk

# Add parent directory to path to import modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_compact_layout():
    """Test the compact layout with reduced component heights"""
    print("Testing Compact Layout...")
    
    try:
        from full_map_gui import FullMapGUI
        
        # Create test window
        root = tk.Tk()
        root.withdraw()  # Hide window
        
        # Create Full Map GUI
        app = FullMapGUI(root)
        root.update_idletasks()
        
        # Get window info
        geometry = root.geometry()
        width = root.winfo_width()
        height = root.winfo_height()
        
        print(f"Window size: {width} x {height}")
        
        # Check component heights
        listbox_height = app.files_listbox.cget('height')
        info_height = app.info_text.cget('height')
        
        print(f"Files listbox: {listbox_height} lines")
        print(f"Info text: {info_height} lines")
        
        # Verify compact sizes
        compact_ok = True
        
        if height > 850:
            print(f"Warning: Window height {height} might be too large")
            compact_ok = False
        
        if listbox_height > 6:
            print(f"Warning: Listbox height {listbox_height} not optimized")
            compact_ok = False
        
        if info_height > 4:
            print(f"Warning: Info text height {info_height} not optimized")
            compact_ok = False
        
        if compact_ok:
            print("Compact layout is optimal")
        
        root.destroy()
        return compact_ok
        
    except Exception as e:
        print(f"Error testing compact layout: {e}")
        return False


def test_scrollbar_functionality():
    """Test scrollbar functionality with multiple files"""
    print("\nTesting Scrollbar Functionality...")
    
    try:
        from full_map_gui import FullMapGUI
        
        # Create test window
        root = tk.Tk()
        root.withdraw()  # Hide window
        
        # Create Full Map GUI
        app = FullMapGUI(root)
        
        # Add multiple test files to trigger scrollbar
        test_files = [
            "test/3AC468-01-C5.map",
            "test/3AC468-02-F0",
            "test/3AC468-03-A0",
            "test/3AC468-04-C3",
            "test/3AC468-05-E6"
        ]
        
        # Add files that exist
        added_files = 0
        for test_file in test_files:
            if os.path.exists(test_file):
                app.map_files.append(test_file)
                app.files_listbox.insert(tk.END, os.path.basename(test_file))
                added_files += 1
        
        print(f"Added {added_files} test files")
        
        # Check if scrollbar is needed
        listbox_height = app.files_listbox.cget('height')
        files_count = len(app.map_files)
        
        print(f"Listbox shows {listbox_height} lines, has {files_count} files")
        
        scrollbar_needed = files_count > listbox_height
        print(f"Scrollbar needed: {scrollbar_needed}")
        
        # Test info text with long content
        app.update_info_display()
        
        # Check if info scrollbar works
        info_content = app.info_text.get("1.0", tk.END)
        info_lines = len(info_content.split('\n'))
        info_height = app.info_text.cget('height')
        
        print(f"Info text shows {info_height} lines, has {info_lines} lines of content")
        
        info_scrollbar_needed = info_lines > info_height
        print(f"Info scrollbar needed: {info_scrollbar_needed}")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"Error testing scrollbar functionality: {e}")
        return False


def test_space_efficiency():
    """Test overall space efficiency"""
    print("\nTesting Space Efficiency...")
    
    try:
        from full_map_gui import FullMapGUI
        from tsk_map_gui import ABMapGUI
        
        # Test Full Map Tool
        root1 = tk.Tk()
        root1.withdraw()
        full_map_app = FullMapGUI(root1)
        root1.update_idletasks()
        
        full_map_height = root1.winfo_height()
        root1.destroy()
        
        # Test AB Map Tool
        root2 = tk.Tk()
        root2.withdraw()
        ab_map_app = ABMapGUI(root2)
        root2.update_idletasks()
        
        ab_map_height = root2.winfo_height()
        root2.destroy()
        
        print(f"Full Map Tool height: {full_map_height}")
        print(f"AB Map Tool height: {ab_map_height}")
        
        height_diff = full_map_height - ab_map_height
        print(f"Height difference: {height_diff:+d}")
        
        # Check if Full Map Tool is reasonably sized
        if height_diff <= 50:
            print("Full Map Tool has efficient space usage")
            return True
        else:
            print("Full Map Tool might still be too tall")
            return False
        
    except Exception as e:
        print(f"Error testing space efficiency: {e}")
        return False


def simulate_user_workflow():
    """Simulate typical user workflow to test usability"""
    print("\nSimulating User Workflow...")
    
    try:
        from full_map_gui import FullMapGUI
        
        # Create test window
        root = tk.Tk()
        root.withdraw()  # Hide window
        
        # Create Full Map GUI
        app = FullMapGUI(root)
        
        # Simulate adding files
        test_files = []
        for i in range(8):  # Add more files than visible
            test_file = f"test_file_{i+1}.map"
            test_files.append(test_file)
            app.map_files.append(test_file)
            app.files_listbox.insert(tk.END, test_file)
        
        print(f"Added {len(test_files)} files to test scrolling")
        
        # Update info display
        app.update_info_display()
        
        # Check if all components are accessible
        listbox_height = app.files_listbox.cget('height')
        files_count = len(app.map_files)
        
        print(f"Listbox displays {listbox_height} files, total {files_count} files")
        print(f"User can scroll to see all {files_count - listbox_height} additional files")
        
        # Check info display
        info_height = app.info_text.cget('height')
        print(f"Info area displays {info_height} lines with scrollbar for more content")
        
        # Verify workflow efficiency
        workflow_ok = True
        
        if listbox_height < 5:
            print("Warning: File list might be too small for efficient use")
            workflow_ok = False
        
        if info_height < 3:
            print("Warning: Info area might be too small for useful information")
            workflow_ok = False
        
        if workflow_ok:
            print("User workflow is efficient with compact layout")
        
        root.destroy()
        return workflow_ok
        
    except Exception as e:
        print(f"Error simulating workflow: {e}")
        return False


def main():
    """Run all space optimization tests"""
    print("Space Optimization Test")
    print("=" * 40)
    
    # Run tests
    test1 = test_compact_layout()
    test2 = test_scrollbar_functionality()
    test3 = test_space_efficiency()
    test4 = simulate_user_workflow()
    
    # Summary
    print("\n" + "=" * 40)
    print("Test Results:")
    print(f"   Compact Layout: {'PASS' if test1 else 'FAIL'}")
    print(f"   Scrollbar Functionality: {'PASS' if test2 else 'FAIL'}")
    print(f"   Space Efficiency: {'PASS' if test3 else 'FAIL'}")
    print(f"   User Workflow: {'PASS' if test4 else 'FAIL'}")
    
    all_passed = test1 and test2 and test3 and test4
    print(f"\nOverall: {'ALL TESTS PASSED' if all_passed else 'SOME TESTS FAILED'}")
    
    if all_passed:
        print("\nSpace optimization successful!")
        print("- Compact layout with scrollbars")
        print("- Efficient space utilization")
        print("- Good user experience")
    else:
        print("\nSome optimization issues detected")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
