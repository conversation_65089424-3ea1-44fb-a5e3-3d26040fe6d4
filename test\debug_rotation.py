#!/usr/bin/env python3
"""
Debug Rotation - Check actual Excel file content for rotation debugging
"""

import sys
import os

# Add parent directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

try:
    from openpyxl import load_workbook
except ImportError:
    print("Error: openpyxl not installed")
    exit(1)


def debug_rotation_file():
    """Debug the 90 degree rotation Excel file"""
    excel_file = os.path.join(current_dir, "fixed_row_headers_90.xlsx")
    
    if not os.path.exists(excel_file):
        print(f"File not found: {excel_file}")
        return
    
    try:
        workbook = load_workbook(excel_file)
        worksheet = workbook.active
        
        print("90° Rotation Excel File Debug")
        print("=" * 40)
        
        # Original dimensions
        original_rows = 9
        original_cols = 271
        print(f"Original dimensions: {original_rows} rows x {original_cols} cols")
        
        # After 90° rotation
        rotated_rows = original_cols  # 271
        rotated_cols = original_rows  # 9
        print(f"After 90° rotation: {rotated_rows} rows x {rotated_cols} cols")
        
        print(f"\nRow Headers (Column D) - First 15 rows:")
        for i in range(1, 16):  # Check first 15 rows
            cell = worksheet.cell(row=i + 1, column=4)
            print(f"  Row {i+1}: {cell.value}")

        print(f"\nColumn Headers (Row 1, starting from Column E):")
        for j in range(1, 10):  # Check all 9 columns
            cell = worksheet.cell(row=1, column=j + 4)
            print(f"  Col {j}: {cell.value}")

        print(f"\nRow Headers (Column D) - Last 10 rows:")
        for i in range(rotated_rows - 9, rotated_rows + 1):  # Check last 10 rows
            if i > 0:
                cell = worksheet.cell(row=i + 1, column=4)
                print(f"  Row {i+1}: {cell.value}")
        
        print(f"\nExpected Logic for 90° Rotation:")
        print(f"  Row headers: 1, 2, 3, ... (representing original column positions)")
        print(f"  Column headers: {original_rows}, {original_rows-1}, {original_rows-2}, ... (original row positions, reversed)")
        print(f"  So column headers should be: 9, 8, 7, 6, 5, ...")
        
        workbook.close()
        
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    debug_rotation_file()
