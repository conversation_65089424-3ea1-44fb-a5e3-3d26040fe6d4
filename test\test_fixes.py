#!/usr/bin/env python3
"""
Test Fixes - Verify the column alignment and data extraction fixes
"""

import sys
import os
sys.path.append('..')
from tsk_map_processor import TSKMapProcessor
from excel_output import create_excel_output


def test_column_alignment_and_data_extraction(filepath):
    """
    Test column alignment and data extraction fixes
    """
    print(f"Testing Fixes: {os.path.basename(filepath)}")
    print("=" * 50)
    
    if not os.path.exists(filepath):
        print(f"❌ File not found: {filepath}")
        return False
    
    processor = TSKMapProcessor()
    
    # Read and parse file
    if not processor.read_file(filepath):
        print("❌ Failed to read file")
        return False
    
    if not processor.parse_file_header():
        print("❌ Failed to parse header")
        return False
    
    print(f"✅ File parsed successfully")
    
    # Test device info extraction with detailed debugging
    print(f"\nDevice Information Extraction:")
    print(f"  Device Name: '{processor.devicename}'")
    print(f"  Lot ID: '{processor.lotid}'")
    print(f"  Wafer Slot ID: '{processor.waferslotid}'")
    print(f"  Start Time: '{processor.waferstarttime}'")
    print(f"  End Time: '{processor.waferendtime}'")
    
    # Debug binary data for B5, B6, B7
    print(f"\nDetailed Binary Analysis for B5, B6, B7:")
    
    # B5 - Wafer ID (位置102-103)
    try:
        wafer_binary = processor.get_binary(processor.filearray, 102, 103)
        wafer_val = processor.binary2val(wafer_binary)
        print(f"  B5 (Wafer ID):")
        print(f"    Position: 102-103")
        print(f"    Binary: {wafer_binary}")
        print(f"    Decimal: {wafer_val}")
        print(f"    Result: '{processor.waferslotid}'")
    except Exception as e:
        print(f"  B5 Error: {e}")
    
    # B6 - Start Time (位置148-159)
    try:
        start_binary = processor.get_binary(processor.filearray, 148, 159)
        print(f"  B6 (Start Time):")
        print(f"    Position: 148-159")
        print(f"    Binary length: {len(start_binary)} bits")
        print(f"    Binary (first 64 bits): {start_binary[:64]}")
        print(f"    Result: '{processor.waferstarttime}'")
        
        # Try manual parsing
        if len(start_binary) >= 96:
            components = []
            for i in range(0, 96, 16):
                byte1 = processor.binary2val(start_binary[i:i+8])
                byte2 = processor.binary2val(start_binary[i+8:i+16])
                value = byte1 + (byte2 << 8)
                components.append(value)
            print(f"    Manual parse components: {components}")
    except Exception as e:
        print(f"  B6 Error: {e}")
    
    # B7 - End Time (位置160-171)
    try:
        end_binary = processor.get_binary(processor.filearray, 160, 171)
        print(f"  B7 (End Time):")
        print(f"    Position: 160-171")
        print(f"    Binary length: {len(end_binary)} bits")
        print(f"    Binary (first 64 bits): {end_binary[:64]}")
        print(f"    Result: '{processor.waferendtime}'")
    except Exception as e:
        print(f"  B7 Error: {e}")
    
    # Process die data for statistics
    if not processor.process_die_data():
        print("❌ Failed to process die data")
        return False
    
    # Test Excel output with fixes
    print(f"\nTesting Excel Output with Fixes:")
    output_filename = "test_fixes_output.xlsx"
    
    try:
        if create_excel_output(processor, "TestSheet", 0, output_filename, filepath):
            print(f"✅ Excel output created: {output_filename}")
            
            if os.path.exists(output_filename):
                file_size = os.path.getsize(output_filename)
                print(f"  File size: {file_size:,} bytes")
                
                print(f"\nExpected Excel Layout:")
                print(f"  Column A: Labels (Device Info., Device Name, etc.)")
                print(f"  Column B: Values")
                print(f"  Column C: Empty")
                print(f"  Column D: Row numbers (1, 2, 3, ...)")
                print(f"  Column E+: Column numbers (1, 2, 3, ...) and map data")
                
                stats = processor.get_test_statistics()
                print(f"\nDevice Info Values:")
                print(f"  B2: Device Name = '{processor.devicename}'")
                print(f"  B4: Lot ID = '{processor.lotid}'")
                print(f"  B5: Wafer ID = '{processor.waferslotid}'")
                print(f"  B6: Start Time = '{processor.waferstarttime}'")
                print(f"  B7: End Time = '{processor.waferendtime}'")
                print(f"  B8: Total Tested = {stats['total_tested']}")
                print(f"  B9: Pass Count = {stats['pass_count']}")
                print(f"  B10: Yield = {stats['yield_percentage']:.2f}%")
                
                return True
            else:
                print("❌ Output file not found")
                return False
        else:
            print("❌ Failed to create Excel output")
            return False
            
    except Exception as e:
        print(f"❌ Error creating Excel output: {e}")
        return False


def main():
    """
    Main test function
    """
    if len(sys.argv) < 2:
        print("Fixes Test")
        print("Usage: python test_fixes.py <tsk_file_path>")
        print("Example: python test_fixes.py ../3AA111-01-B4")
        return
    
    filepath = sys.argv[1]
    
    print("TSK/MAP Fixes Test")
    print("Testing column alignment and data extraction fixes")
    print("=" * 60)
    
    if test_column_alignment_and_data_extraction(filepath):
        print("\n" + "=" * 60)
        print("🎉 Fixes Test Completed!")
        print("\nFixed Issues:")
        print("✅ Column alignment: Map data now starts from column E")
        print("✅ Row/column headers: Now in column D and row 1 (E+)")
        print("✅ Wafer ID extraction: Improved binary to number conversion")
        print("✅ Time parsing: Enhanced with multiple parsing methods")
        
        print(f"\nPlease check the Excel file to verify:")
        print(f"1. Device info in columns A-D")
        print(f"2. Map row numbers in column D")
        print(f"3. Map column numbers in row 1 starting from column E")
        print(f"4. Map data starting from cell E2")
    else:
        print("\n❌ Fixes test failed")


if __name__ == "__main__":
    main()
