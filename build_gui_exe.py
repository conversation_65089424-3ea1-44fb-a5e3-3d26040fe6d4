#!/usr/bin/env python3
"""
GUI-only build script for TSK/MAP Tool
Creates executable without console window
Author: Yuribytes
Supports both 32-bit and 64-bit systems
"""

import os
import sys
import subprocess
import shutil
import platform
from datetime import datetime


def check_pyinstaller():
    """Check if PyInstaller is installed"""
    try:
        import PyInstaller
        print(f"✅ PyInstaller is installed (version: {PyInstaller.__version__})")
        return True
    except ImportError:
        print("❌ PyInstaller is not installed")
        print("Installing PyInstaller...")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pyinstaller'])
            print("✅ PyInstaller installed successfully")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install PyInstaller: {e}")
            return False


def get_system_info():
    """Get system information"""
    arch = platform.architecture()[0]
    system = platform.system()
    machine = platform.machine()
    
    print(f"System: {system}")
    print(f"Architecture: {arch}")
    print(f"Machine: {machine}")
    
    return {
        'system': system,
        'arch': arch,
        'machine': machine,
        'is_64bit': arch == '64bit'
    }


def create_version_file():
    """Create version file for Windows executable"""
    version_content = '''# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    # filevers and prodvers should be always a tuple with four items: (1, 2, 3, 4)
    # Set not needed items to zero 0.
    filevers=(1,0,0,0),
    prodvers=(1,0,0,0),
    # Contains a bitmask that specifies the valid bits 'flags'r
    mask=0x3f,
    # Contains a bitmask that specifies the Boolean attributes of the file.
    flags=0x0,
    # The operating system for which this file was designed.
    # 0x4 - NT and there is no need to change it.
    OS=0x4,
    # The general type of file.
    # 0x1 - the file is an application.
    fileType=0x1,
    # The function of the file.
    # 0x0 - the function is not defined for this fileType
    subtype=0x0,
    # Creation date and time stamp.
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'Chipone TE development Team'),
        StringStruct(u'FileDescription', u'TSK/MAP File Processor Tool'),
        StringStruct(u'FileVersion', u'1.0.0'),
        StringStruct(u'InternalName', u'TSK_MAP_Tool'),
        StringStruct(u'LegalCopyright', u'Copyright (C) 2025 Yuribytes'),
        StringStruct(u'OriginalFilename', u'TSK_MAP_Tool.exe'),
        StringStruct(u'ProductName', u'TSK/MAP File Processor'),
        StringStruct(u'ProductVersion', u'1.0.0'),
        StringStruct(u'Author', u'Yuribytes')])
      ]),
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)'''
    
    with open('version_file.txt', 'w', encoding='utf-8') as f:
        f.write(version_content)
    
    print("✅ Version file created: version_file.txt")


def create_gui_spec():
    """Create PyInstaller spec file for GUI mode (no console)"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-
# TSK/MAP File Processor - GUI Only Version
# Author: Yuribytes

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'openpyxl',
        'openpyxl.workbook',
        'openpyxl.worksheet',
        'openpyxl.styles',
        'openpyxl.utils',
        'openpyxl.cell',
        'openpyxl.formatting',
        'openpyxl.utils.cell',
        'tkinter',
        'tkinter.ttk',
        'tkinter.filedialog',
        'tkinter.messagebox',
        'tkinter.simpledialog',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL',
        'cv2',
        'pytest',
        'IPython',
        'jupyter',
        'sphinx',
        'setuptools',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='TSK_MAP_Tool',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 关键：设置为 False 以隐藏控制台
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    version='version_file.txt',  # 添加版本信息
    icon=None,
)
'''
    
    with open('TSK_MAP_Tool_GUI.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ GUI spec file created: TSK_MAP_Tool_GUI.spec")


def build_gui_exe():
    """Build the GUI executable (no console)"""
    print("Building GUI executable (no console window)...")
    
    # Create version file
    create_version_file()
    
    # Create spec file
    create_gui_spec()
    
    # Build command
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--clean',
        '--noconfirm',
        'TSK_MAP_Tool_GUI.spec'
    ]
    
    print(f"Running: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True)
        print("✅ GUI build completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ GUI build failed: {e}")
        return False


def create_gui_distribution(system_info):
    """Create GUI distribution package"""
    print("Creating GUI distribution package...")
    
    # Get version info
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    arch_suffix = "x64" if system_info['is_64bit'] else "x86"
    
    # Package name
    package_name = f"TSK_MAP_Tool_GUI_{arch_suffix}_{timestamp}"
    package_dir = f"dist/{package_name}"
    
    # Create package directory
    os.makedirs(package_dir, exist_ok=True)
    
    # Copy executable
    exe_name = "TSK_MAP_Tool.exe"
    if os.path.exists(f"dist/{exe_name}"):
        shutil.copy2(f"dist/{exe_name}", f"{package_dir}/{exe_name}")
        print(f"✅ Copied GUI executable to {package_dir}")
    else:
        print(f"❌ GUI executable not found: dist/{exe_name}")
        return False
    
    # Create README
    readme_content = f"""# TSK/MAP File Processor Tool (GUI Version)

## Version Information
- Version: 1.0.0
- Author: Yuribytes
- Company: Chipone TE development Team
- Architecture: {system_info['arch']}
- Built on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- Python Version: {sys.version.split()[0]}

## Features
- Pure GUI application (no console window)
- Process TSK and MAP files
- AB comparison analysis with 6 detailed sheets:
  * Summary: Bin difference statistics
  * Correlation: Bin jump analysis matrix
  * Map_compare: Position-by-position comparison (Different first, Same last)
  * Map_compare_full: Combined format comparison (most intuitive view)
  * Amap/Bmap: Original data sheets
  * Setup: Configuration information
- Support for file rotation (0°, 90°, 180°, 270°)
- Professional Excel reports with color coding
- User-friendly graphical interface

## Quick Start
1. Double-click TSK_MAP_Tool.exe to start the application
2. Select your desired tool:
   - AB Map Tool: For single file processing and comparison analysis (recommended)
   - Full Map Tool: For batch processing
3. For AB comparison:
   - Choose "AB Compare" mode
   - Select your Amap and Bmap files
   - Configure rotation angle if needed
   - Click PROCESS to generate analysis report
4. Check the Map_compare_full sheet for the most intuitive comparison view

## Color Coding in Map_compare_full Sheet
- 🟢 Green: Same bin values, both Pass (ideal state)
- 🔴 Red: Same bin values, both Fail (consistent failure)
- 🟠 Orange: Same bin values, mixed Pass/Fail (needs attention)
- 🟡 Yellow: Different bin values (requires investigation)
- 🔵 Light Blue: Only Amap has data
- 🟨 Light Yellow: Only Bmap has data

## System Requirements
- Windows 7/10/11 ({system_info['arch']})
- Minimum 4GB RAM recommended
- 1GB free disk space

## Usage Tips
1. The application runs in pure GUI mode without console window
2. For best results, use files from the same test batch
3. The Map_compare_full sheet provides the most intuitive comparison
4. Green cells indicate perfect matching, yellow cells need attention
5. Use the Summary sheet for overall statistics

## Technical Information
- Standalone executable with all dependencies included
- No Python installation required
- Built with PyInstaller for maximum compatibility
- Optimized for Windows systems

## Support
This is a professional tool developed by Yuribytes for Chipone TE development Team.
For technical support, please contact the development team.

## Copyright
Copyright (C) 2025 Yuribytes
All rights reserved.
"""
    
    with open(f"{package_dir}/README.txt", 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"✅ Created README.txt")
    
    # Create desktop shortcut batch (optional)
    shortcut_content = f"""@echo off
REM TSK/MAP File Processor Tool Launcher
REM Author: Yuribytes
REM Company: Chipone TE development Team

title TSK/MAP File Processor
start "" "{exe_name}"
"""
    
    with open(f"{package_dir}/Launch_TSK_MAP_Tool.bat", 'w', encoding='utf-8') as f:
        f.write(shortcut_content)
    
    print(f"✅ Created Launch_TSK_MAP_Tool.bat")
    
    return package_dir


def test_gui_exe(package_dir):
    """Test the GUI executable"""
    print("Testing GUI executable...")
    
    exe_path = os.path.join(package_dir, "TSK_MAP_Tool.exe")
    
    if not os.path.exists(exe_path):
        print(f"❌ GUI executable not found: {exe_path}")
        return False
    
    # For GUI applications, we can't easily test with command line
    # Just check if the file exists and has reasonable size
    file_size = os.path.getsize(exe_path)
    if file_size > 1024 * 1024:  # At least 1MB
        print(f"✅ GUI executable appears valid (size: {file_size / (1024*1024):.1f} MB)")
        return True
    else:
        print(f"❌ GUI executable seems too small: {file_size} bytes")
        return False


def cleanup():
    """Clean up build files"""
    print("Cleaning up build files...")
    
    cleanup_items = [
        'build',
        'TSK_MAP_Tool_GUI.spec',
        'version_file.txt',
        '__pycache__'
    ]
    
    for item in cleanup_items:
        if os.path.exists(item):
            if os.path.isdir(item):
                shutil.rmtree(item)
            else:
                os.remove(item)
            print(f"✅ Removed {item}")


def main():
    """Main build process for GUI version"""
    print("TSK/MAP Tool - GUI-Only Executable Builder")
    print("Author: Yuribytes")
    print("No Console Window - Pure GUI Experience")
    print("=" * 60)
    
    # Check PyInstaller
    if not check_pyinstaller():
        return False
    
    print()
    
    # Get system info
    system_info = get_system_info()
    print()
    
    # Build GUI executable
    if not build_gui_exe():
        print("❌ GUI build failed")
        return False
    
    print()
    
    # Create distribution
    package_dir = create_gui_distribution(system_info)
    if not package_dir:
        print("❌ GUI distribution creation failed")
        return False
    
    print()
    
    # Test executable
    test_gui_exe(package_dir)
    
    print()
    
    # Cleanup
    cleanup()
    
    print()
    print("=" * 70)
    print("🎉 GUI build completed successfully!")
    print(f"📦 Package location: {package_dir}")
    
    # Show file size
    exe_path = os.path.join(package_dir, "TSK_MAP_Tool.exe")
    if os.path.exists(exe_path):
        size_mb = os.path.getsize(exe_path) / (1024 * 1024)
        print(f"📊 GUI executable size: {size_mb:.1f} MB")
    
    print("\n🚀 GUI version ready for distribution!")
    print("✨ Features:")
    print("   - No console window (pure GUI)")
    print("   - Professional version information")
    print("   - Author: Yuribytes")
    print("   - Company: Chipone TE development Team")
    print(f"\n📁 Files in {package_dir}:")
    for file in os.listdir(package_dir):
        print(f"   - {file}")
    
    return True


if __name__ == "__main__":
    success = main()
    
    if not success:
        print("\n❌ GUI build process failed!")
        input("Press Enter to exit...")
        sys.exit(1)
    else:
        print("\n✅ GUI build process completed successfully!")
        input("Press Enter to exit...")
        sys.exit(0)
