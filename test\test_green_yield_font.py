#!/usr/bin/env python3
"""
Test script for the green yield font functionality in Bin_Summary sheet
Tests that all yield percentage values are displayed in green color
"""

import os
import sys
import tempfile

# Add parent directory to path to import modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_green_yield_font_functionality():
    """Test that all yield percentages are displayed in green"""
    print("🟢 Testing Green Yield Font Functionality")
    print("=" * 50)
    
    try:
        from full_map_processor import FullMapProcessor
        from config_reader import ConfigReader
        from openpyxl import Workbook
        
        # Create test setup
        processor = FullMapProcessor()
        
        # Create mock config
        config_reader = ConfigReader()
        config_reader.device_name = "GreenYield_TestChip"
        config_reader.vendor_name = "ColorTest_Corp"
        processor.set_config_reader(config_reader)
        
        # Create test workbook
        workbook = Workbook()
        worksheet = workbook.active
        worksheet.title = "Bin_Summary"
        
        # Create mock processors with different yield values
        mock_processors = {
            "GREEN_TEST-W01.map": {
                "processor": MockTSKProcessor(2000, 1900),  # 95% yield
                "file_path": "GREEN_TEST-W01.map"
            },
            "GREEN_TEST-W02.map": {
                "processor": MockTSKProcessor(2100, 1680),  # 80% yield
                "file_path": "GREEN_TEST-W02.map"
            },
            "GREEN_TEST-W03.map": {
                "processor": MockTSKProcessor(1800, 1260),  # 70% yield
                "file_path": "GREEN_TEST-W03.map"
            }
        }
        
        print("1️⃣ Creating mock sheets for data rows...")
        # Create mock sheets to generate data rows
        for filename in mock_processors.keys():
            sheet_name = processor._sanitize_sheet_name(os.path.splitext(filename)[0])
            mock_sheet = workbook.create_sheet(title=sheet_name)
            mock_sheet['A1'] = f"Mock data for {sheet_name}"

        print("2️⃣ Creating Bin_Summary with yield values...")
        processor._create_bin_summary_sheet(worksheet, mock_processors, workbook)
        
        # Test 1: Check header yield percentage (I2)
        print("3️⃣ Testing header yield percentage color...")
        header_yield_cell = worksheet['I2']
        if header_yield_cell.font.color and header_yield_cell.font.color.rgb:
            color = header_yield_cell.font.color.rgb
            # Check for green color (with or without alpha channel)
            if '28A745' in color:  # Green color (may have alpha prefix)
                print(f"   ✅ Header yield percentage is green: #{color}")
                header_test = True
            else:
                print(f"   ❌ Header yield color is #{color}, expected green (28A745)")
                header_test = False
        else:
            print("   ❌ Header yield cell has no color information")
            header_test = False
        
        # Test 2: Check yield column values (column B, rows 6+)
        print("4️⃣ Testing yield column values color...")
        yield_column_test = True
        yield_cells_checked = 0
        
        for row in range(6, 15):  # Check data rows
            cell = worksheet.cell(row=row, column=2)  # Column B (Yield%)
            if cell.value is not None:
                yield_cells_checked += 1
                if cell.font.color and cell.font.color.rgb:
                    color = cell.font.color.rgb
                    if '28A745' in color:  # Green color (may have alpha prefix)
                        print(f"   ✅ Row {row} yield value is green: {cell.value}")
                    else:
                        print(f"   ❌ Row {row} yield color is #{color}, expected green")
                        yield_column_test = False
                else:
                    print(f"   ❌ Row {row} yield cell has no color information")
                    yield_column_test = False
        
        print(f"   Checked {yield_cells_checked} yield cells in data rows")
        
        # Test 3: Check average row yield (last row with data)
        print("5️⃣ Testing average row yield color...")
        max_row = worksheet.max_row
        avg_yield_cell = None
        
        # Find the average row (look for "Average" in column A)
        for row in range(max_row, 5, -1):
            cell_a = worksheet.cell(row=row, column=1)
            if cell_a.value and "Average" in str(cell_a.value):
                avg_yield_cell = worksheet.cell(row=row, column=2)
                break
        
        avg_test = False
        if avg_yield_cell and avg_yield_cell.value is not None:
            if avg_yield_cell.font.color and avg_yield_cell.font.color.rgb:
                color = avg_yield_cell.font.color.rgb
                if '28A745' in color:  # Green color (may have alpha prefix)
                    print(f"   ✅ Average yield is green: {avg_yield_cell.value}")
                    avg_test = True
                else:
                    print(f"   ❌ Average yield color is #{color}, expected green")
            else:
                print("   ❌ Average yield cell has no color information")
        else:
            print("   ❌ Could not find average yield cell")
        
        workbook.close()
        return header_test and yield_column_test and avg_test
        
    except Exception as e:
        print(f"❌ Error during green font testing: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_green_font_visual_demo():
    """Create a visual demo showing green yield fonts"""
    print("\n🟢 Creating Green Font Visual Demo")
    print("=" * 50)
    
    try:
        from full_map_processor import FullMapProcessor
        from config_reader import ConfigReader
        from openpyxl import Workbook
        
        # Create processor
        processor = FullMapProcessor()
        
        # Setup config
        config_reader = ConfigReader()
        config_reader.device_name = "GreenFont_Demo_V3.0"
        config_reader.vendor_name = "VisualDemo_Solutions"
        processor.set_config_reader(config_reader)
        
        # Create workbook
        workbook = Workbook()
        worksheet = workbook.active
        worksheet.title = "Bin_Summary"
        
        # Mock data with various yield values to show green font
        mock_processors = {
            "DEMO_GREEN-W01.map": {"processor": MockTSKProcessor(3000, 2850), "file_path": "DEMO_GREEN-W01.map"},  # 95%
            "DEMO_GREEN-W02.map": {"processor": MockTSKProcessor(2800, 2520), "file_path": "DEMO_GREEN-W02.map"},  # 90%
            "DEMO_GREEN-W03.map": {"processor": MockTSKProcessor(3200, 2560), "file_path": "DEMO_GREEN-W03.map"},  # 80%
            "DEMO_GREEN-W04.map": {"processor": MockTSKProcessor(2900, 2030), "file_path": "DEMO_GREEN-W04.map"},  # 70%
        }
        
        # Create mock sheets for data rows
        print("1️⃣ Creating mock sheets for data rows...")
        for filename in mock_processors.keys():
            sheet_name = processor._sanitize_sheet_name(os.path.splitext(filename)[0])
            mock_sheet = workbook.create_sheet(title=sheet_name)
            mock_sheet['A1'] = f"Mock data for {sheet_name}"

        # Create the complete formatted sheet
        print("2️⃣ Creating complete Bin_Summary with green yield fonts...")
        processor._create_bin_summary_sheet(worksheet, mock_processors, workbook)
        
        # Save sample file
        sample_file = os.path.join(tempfile.gettempdir(), "Bin_Summary_Green_Yield_Demo.xlsx")
        workbook.save(sample_file)
        workbook.close()
        
        print(f"✅ Green yield demo file created: {sample_file}")
        print("   Features demonstrated:")
        print("   • Header yield percentage in green")
        print("   • All data row yield percentages in green")
        print("   • Average row yield percentage in green")
        print("   • Consistent green color across all yield values")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating green font demo: {e}")
        return False


def test_color_consistency():
    """Test that green color is consistent across all yield locations"""
    print("\n🟢 Testing Color Consistency")
    print("=" * 50)
    
    try:
        from full_map_processor import FullMapProcessor
        from openpyxl import Workbook
        
        # Create processor
        processor = FullMapProcessor()
        processor._setup_summary_styles()
        
        # Test that yield_good font is green
        print("1️⃣ Testing yield_good font color...")
        yield_font = processor.fonts['yield_good']
        if yield_font.color and yield_font.color.rgb:
            color = yield_font.color.rgb
            if '28A745' in color:  # Green color (may have alpha prefix)
                print(f"   ✅ yield_good font is green: #{color}")
            else:
                print(f"   ❌ yield_good font color is #{color}, expected green")
                return False
        else:
            print("   ❌ yield_good font has no color")
            return False
        
        # Test color hex value
        print("2️⃣ Testing green color specification...")
        expected_green = '28A745'  # Bootstrap success green
        if expected_green in str(yield_font.color.rgb):
            print(f"   ✅ Using correct green color: #{expected_green}")
        else:
            print(f"   ❌ Green color mismatch")
            return False
        
        print("3️⃣ Testing color application methods...")
        
        # Test that all yield-related methods use the same green
        test_workbook = Workbook()
        test_worksheet = test_workbook.active
        
        # Test header yield color application
        test_worksheet['I2'].value = "85.50%"
        test_worksheet['I2'].font = processor.fonts['yield_good']
        
        # Test data row yield color (simulated)
        from openpyxl.styles import Font
        data_yield_font = Font(name='Calibri', size=10, bold=True, color='28A745')
        test_worksheet['B6'].value = "92.31%"
        test_worksheet['B6'].font = data_yield_font
        
        # Test average yield color (simulated)
        avg_yield_font = Font(name='Calibri', size=11, bold=True, color='28A745')
        test_worksheet['B12'].value = "88.75%"
        test_worksheet['B12'].font = avg_yield_font
        
        # Verify all have the same green color
        colors = [
            test_worksheet['I2'].font.color.rgb,
            test_worksheet['B6'].font.color.rgb,
            test_worksheet['B12'].font.color.rgb
        ]
        
        if all(color == colors[0] for color in colors):
            print("   ✅ All yield locations use consistent green color")
            consistency_test = True
        else:
            print("   ❌ Inconsistent colors across yield locations")
            print(f"      Header: {colors[0]}")
            print(f"      Data: {colors[1]}")
            print(f"      Average: {colors[2]}")
            consistency_test = False
        
        test_workbook.close()
        return consistency_test
        
    except Exception as e:
        print(f"❌ Error during consistency testing: {e}")
        return False


class MockTSKProcessor:
    """Mock TSKMapProcessor for testing"""
    def __init__(self, total_tested, pass_count):
        self.total_tested = total_tested
        self.pass_count = pass_count
    
    def get_test_statistics(self):
        return {
            'total_tested': self.total_tested,
            'pass_count': self.pass_count,
            'yield_percentage': (self.pass_count / self.total_tested * 100) if self.total_tested > 0 else 0.0
        }


def main():
    """Run all green yield font tests"""
    print("🟢 GREEN YIELD FONT TEST SUITE")
    print("=" * 70)
    
    # Run tests
    test1 = test_green_yield_font_functionality()
    test2 = test_green_font_visual_demo()
    test3 = test_color_consistency()
    
    # Summary
    print("\n" + "=" * 70)
    print("📋 GREEN FONT TEST RESULTS")
    print("=" * 70)
    print(f"   Yield Font Functionality: {'PASS' if test1 else 'FAIL'}")
    print(f"   Visual Demo: {'PASS' if test2 else 'FAIL'}")
    print(f"   Color Consistency: {'PASS' if test3 else 'FAIL'}")
    
    all_passed = test1 and test2 and test3
    print(f"\nOVERALL: {'ALL TESTS PASSED' if all_passed else 'SOME TESTS FAILED'}")
    
    if all_passed:
        print("\n🎉 SUCCESS! Green yield font functionality is working perfectly!")
        print("\n🟢 GREEN YIELD FEATURES:")
        print("   ✅ Header yield percentage in green")
        print("   ✅ All data row yield percentages in green")
        print("   ✅ Average row yield percentage in green")
        print("   ✅ Consistent green color (#28A745)")
        print("   ✅ Professional appearance maintained")
        print("   ✅ Matches the specification exactly")
    else:
        print("\n❌ Some green font tests failed. Please check the implementation.")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
