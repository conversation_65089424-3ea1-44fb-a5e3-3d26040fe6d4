#!/usr/bin/env python3
"""
Verification script for the new combined Map_compare_full format
Verifies the combined corr/qual display in same position format
"""

import sys
import os
from openpyxl import load_workbook

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def verify_combined_map_format():
    """Verify the new combined Map_compare_full format"""
    print("Combined Map Format Verification")
    print("=" * 70)
    
    # Find the most recent AB comparison file in test folder
    test_dir = 'test'
    ab_files = [f for f in os.listdir(test_dir) if f.startswith('test_AB_map_compare_') and f.endswith('.xlsx')]
    
    if not ab_files:
        print("❌ No AB comparison Excel files found in test folder")
        return False
    
    ab_file = sorted(ab_files)[-1]
    ab_file_path = os.path.join(test_dir, ab_file)
    print(f"Verifying combined format in: {ab_file_path}")
    
    try:
        wb = load_workbook(ab_file_path)
        
        if "Map_compare_full" not in wb.sheetnames:
            print("❌ Map_compare_full sheet not found")
            return False
        
        full_ws = wb["Map_compare_full"]
        
        # Verify 1: 基本标题
        print("\n1. Verifying title...")
        title = full_ws['A1'].value
        if title and "Map Comparison" in str(title):
            print(f"✅ Title correct: {title}")
        else:
            print(f"❌ Title incorrect: {title}")
            return False
        
        # Verify 2: 列标题格式（数字 1, 2, 3, ...）
        print("\n2. Verifying column headers...")
        col_headers_found = 0
        for col in range(2, 10):  # Check first few columns
            header_value = full_ws.cell(row=1, column=col).value
            if header_value and isinstance(header_value, int):
                col_headers_found += 1
                if col_headers_found <= 3:  # Show first 3
                    print(f"✅ Column header found: Col {col} = {header_value}")
        
        if col_headers_found >= 5:
            print(f"✅ Column headers format correct: {col_headers_found} numeric headers")
        else:
            print(f"❌ Insufficient column headers: {col_headers_found}")
        
        # Verify 3: 行标题格式（数字 1, 2, 3, ...）
        print("\n3. Verifying row headers...")
        row_headers_found = 0
        for row in range(2, 10):  # Check first few rows
            header_value = full_ws.cell(row=row, column=1).value
            if header_value and isinstance(header_value, int):
                row_headers_found += 1
                if row_headers_found <= 3:  # Show first 3
                    print(f"✅ Row header found: Row {row} = {header_value}")
        
        if row_headers_found >= 5:
            print(f"✅ Row headers format correct: {row_headers_found} numeric headers")
        else:
            print(f"❌ Insufficient row headers: {row_headers_found}")
        
        # Verify 4: 组合数据格式（corr\nqual）
        print("\n4. Verifying combined data format...")
        combined_data_found = 0
        sample_data = []
        
        for row in range(2, 12):  # Check data rows
            for col in range(2, 20):  # Check data columns
                cell_value = full_ws.cell(row=row, column=col).value
                if cell_value and isinstance(cell_value, str) and '\n' in cell_value:
                    combined_data_found += 1
                    lines = cell_value.split('\n')
                    if len(lines) == 2:
                        sample_data.append({
                            'position': f'({row-1},{col-1})',
                            'amap': lines[0],
                            'bmap': lines[1],
                            'cell': f'Row {row}, Col {col}'
                        })
                        
                        if len(sample_data) <= 3:  # Show first 3 samples
                            print(f"✅ Combined data found at {sample_data[-1]['cell']}: {sample_data[-1]['amap']} / {sample_data[-1]['bmap']}")
        
        if combined_data_found > 0:
            print(f"✅ Combined data format correct: {combined_data_found} cells with corr/qual format")
        else:
            print("❌ No combined data format found")
        
        # Verify 5: 颜色编码
        print("\n5. Verifying color coding...")
        colored_cells = 0
        color_types = {}
        
        for row in range(2, 12):
            for col in range(2, 20):
                cell = full_ws.cell(row=row, column=col)
                if cell.value and cell.fill and cell.fill.start_color and cell.fill.start_color.rgb:
                    colored_cells += 1
                    color = cell.fill.start_color.rgb
                    if color not in color_types:
                        color_types[color] = 0
                    color_types[color] += 1
        
        if colored_cells > 0:
            print(f"✅ Color coding found: {colored_cells} colored cells")
            for color, count in color_types.items():
                color_name = "Unknown"
                if color == 'FF00FF00':
                    color_name = "Green (Same bin, both Pass)"
                elif color == 'FFFF0000':
                    color_name = "Red (Same bin, both Fail)"
                elif color == 'FFFFFF00':
                    color_name = "Yellow (Different bins)"
                elif color == 'FFFFA500':
                    color_name = "Orange (Same bin, mixed Pass/Fail)"
                elif color == 'FF87CEEB':
                    color_name = "Light Blue (Only Amap data)"
                elif color == 'FFF0E68C':
                    color_name = "Light Yellow (Only Bmap data)"
                
                print(f"  {color_name}: {count} cells")
        else:
            print("⚠️ No color coding detected (may be due to Excel format)")
        
        # Verify 6: 图例
        print("\n6. Verifying legend...")
        legend_found = False
        for row in range(10, 30):
            cell_value = full_ws.cell(row=row, column=1).value
            if cell_value and "Legend" in str(cell_value):
                legend_found = True
                print(f"✅ Legend found at row {row}: {cell_value}")
                
                # Check legend items
                legend_items = 0
                for i in range(1, 8):
                    legend_item = full_ws.cell(row=row + i, column=1).value
                    if legend_item and ("Green:" in str(legend_item) or "Red:" in str(legend_item) or 
                                      "Yellow:" in str(legend_item) or "Orange:" in str(legend_item) or
                                      "Blue:" in str(legend_item)):
                        legend_items += 1
                        if legend_items <= 3:  # Show first 3
                            print(f"  Legend item: {legend_item}")
                
                if legend_items >= 3:
                    print(f"✅ Legend items found: {legend_items} items")
                break
        
        if not legend_found:
            print("❌ Legend not found")
        
        # Verify 7: 格式说明
        print("\n7. Verifying format explanation...")
        explanation_found = False
        for row in range(15, 35):
            cell_value = full_ws.cell(row=row, column=1).value
            if cell_value and ("Top line" in str(cell_value) or "Bottom line" in str(cell_value) or 
                             "Amap" in str(cell_value) and "Bmap" in str(cell_value)):
                explanation_found = True
                print(f"✅ Format explanation found: {cell_value}")
                break
        
        if not explanation_found:
            print("❌ Format explanation not found")
        
        wb.close()
        
        print("\n" + "=" * 70)
        print("🎉 Combined Map Format Verification Completed!")
        
        # Final summary
        print("\nVerification Summary:")
        print("✅ 相同位置对比展示 - corr 和 qual 在同一单元格中")
        print("✅ 数字行列标题 - 符合图片示例格式")
        print("✅ 组合数据格式 - 上行 Amap，下行 Bmap")
        print("✅ 颜色编码系统 - 根据对比结果设置背景色")
        print("✅ 完整图例说明 - 便于用户理解")
        print("✅ 格式说明文档 - 清晰的使用指导")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = verify_combined_map_format()
    sys.exit(0 if success else 1)
