#!/usr/bin/env python3
"""
Verify Full Map Output - Check the generated Excel file from Full Map Tool
"""

import sys
import os

try:
    from openpyxl import load_workbook
except ImportError:
    print("Error: openpyxl not installed. Please run: pip install openpyxl")
    exit(1)


def verify_full_map_excel(excel_file):
    """Verify the full map Excel file structure and content"""
    print(f"Verifying Full Map Excel: {os.path.basename(excel_file)}")
    print("=" * 60)
    
    if not os.path.exists(excel_file):
        print(f"❌ Excel file not found: {excel_file}")
        return False
    
    try:
        # Load Excel workbook
        workbook = load_workbook(excel_file)
        
        print(f"✅ Excel file loaded successfully")
        print(f"  File size: {os.path.getsize(excel_file):,} bytes")
        print(f"  Sheets: {len(workbook.sheetnames)}")
        
        # List all sheets
        print(f"\nSheet Information:")
        for i, sheet_name in enumerate(workbook.sheetnames, 1):
            worksheet = workbook[sheet_name]
            max_row = worksheet.max_row
            max_col = worksheet.max_column
            
            print(f"  {i}. '{sheet_name}':")
            print(f"     Dimensions: {max_row} rows x {max_col} columns")
            
            # Check device info section
            device_name = worksheet['B2'].value
            lot_id = worksheet['B4'].value
            test_program = worksheet['B3'].value
            
            print(f"     Device Name: {device_name}")
            print(f"     Test Program: {test_program}")
            print(f"     Lot ID: {lot_id}")
            
            # Check bin statistics section
            category_header = worksheet['A13'].value
            if category_header == "Category":
                print(f"     ✅ Bin statistics section found")
                
                # Count bin entries
                bin_count = 0
                for row in range(14, min(max_row + 1, 50)):  # Check up to row 50
                    cell_value = worksheet.cell(row=row, column=1).value
                    if cell_value is not None and str(cell_value).strip():
                        bin_count += 1
                    else:
                        break
                
                print(f"     Bin entries: {bin_count}")
                
                # Check if bin names are bold
                first_bin_cell = worksheet['A14']
                is_bold = first_bin_cell.font.bold if first_bin_cell.font else False
                print(f"     Bin names bold: {is_bold}")
                
            else:
                print(f"     ⚠️  Bin statistics section not found")
        
        workbook.close()
        
        print(f"\n✅ Full Map Excel verification completed")
        return True
        
    except Exception as e:
        print(f"❌ Error verifying Excel file: {e}")
        import traceback
        traceback.print_exc()
        return False


def compare_sheets_content(excel_file):
    """Compare content between different sheets"""
    print(f"\nComparing Sheet Contents")
    print("=" * 40)
    
    if not os.path.exists(excel_file):
        return False
    
    try:
        workbook = load_workbook(excel_file)
        
        if len(workbook.sheetnames) < 2:
            print("Only one sheet found, no comparison needed")
            workbook.close()
            return True
        
        # Compare first two sheets
        sheet1_name = workbook.sheetnames[0]
        sheet2_name = workbook.sheetnames[1]
        
        sheet1 = workbook[sheet1_name]
        sheet2 = workbook[sheet2_name]
        
        print(f"Comparing '{sheet1_name}' vs '{sheet2_name}':")
        
        # Compare device info
        device1 = sheet1['B2'].value
        device2 = sheet2['B2'].value
        
        if device1 == device2:
            print(f"  ✅ Device names match: {device1}")
        else:
            print(f"  ⚠️  Device names differ: '{device1}' vs '{device2}'")
        
        # Compare bin statistics
        bin1_count = 0
        bin2_count = 0
        
        for row in range(14, 50):
            if sheet1.cell(row=row, column=1).value:
                bin1_count += 1
            if sheet2.cell(row=row, column=1).value:
                bin2_count += 1
        
        if bin1_count == bin2_count:
            print(f"  ✅ Bin counts match: {bin1_count}")
        else:
            print(f"  ⚠️  Bin counts differ: {bin1_count} vs {bin2_count}")
        
        workbook.close()
        return True
        
    except Exception as e:
        print(f"❌ Error comparing sheets: {e}")
        return False


def main():
    """Main verification function"""
    print("Full Map Output Verification")
    print("Checking generated Excel files from Full Map Tool")
    print("=" * 70)
    
    # Files to check
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(current_dir)
    
    test_files = [
        os.path.join(parent_dir, "all_maps_summary.xlsx"),
        os.path.join(current_dir, "test_single_file.xlsx")
    ]
    
    success_count = 0
    total_count = 0
    
    for test_file in test_files:
        if os.path.exists(test_file):
            total_count += 1
            if verify_full_map_excel(test_file):
                success_count += 1
                compare_sheets_content(test_file)
            print()  # Add spacing
        else:
            print(f"⚠️  Test file not found: {os.path.basename(test_file)}")
            print()
    
    print("=" * 70)
    print(f"Verification Summary: {success_count}/{total_count} files verified")
    
    if success_count == total_count and total_count > 0:
        print("🎉 All Full Map Excel files verified successfully!")
        print("\nFull Map Tool Features Verified:")
        print("✅ Multiple sheets in single Excel file")
        print("✅ Sheet names based on MAP file names")
        print("✅ Complete device information in each sheet")
        print("✅ Bin statistics with bold formatting")
        print("✅ Configuration file integration")
        print("✅ Consistent data structure across sheets")
        
        print(f"\nArchitecture Verification:")
        print(f"• Code reuse from AB Map Tool ✓")
        print(f"• ExcelOutputHandler reused ✓")
        print(f"• ConfigReader integration ✓")
        print(f"• TSKMapProcessor per file ✓")
        print(f"• Multi-sheet Excel generation ✓")
        
    elif success_count > 0:
        print(f"⚠️  {success_count} out of {total_count} files verified")
    else:
        print("❌ No files were successfully verified")
    
    print(f"\nGenerated Files:")
    if os.path.exists(os.path.join(parent_dir, "all_maps_summary.xlsx")):
        print(f"• all_maps_summary.xlsx (multiple MAP files)")
    if os.path.exists(os.path.join(current_dir, "test_single_file.xlsx")):
        print(f"• test/test_single_file.xlsx (single MAP file)")


if __name__ == "__main__":
    main()
