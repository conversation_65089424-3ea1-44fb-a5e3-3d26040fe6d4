#!/usr/bin/env python3
"""
Test script to verify GUI integration with rotation angle naming
"""

import tkinter as tk
from full_map_tool_frame import FullMapToolFrame
import os

def test_gui_rotation_naming():
    """Test GUI with rotation angle naming"""
    
    print("🖥️  Testing GUI with rotation angle naming...")
    print("=" * 60)
    
    # Create root window
    root = tk.Tk()
    root.title("Full Map Tool - Rotation Naming Test")
    
    # Create frame
    frame = FullMapToolFrame(root)
    frame.pack(fill=tk.BOTH, expand=True)
    
    # Add test file
    test_file = 'test/3AA111-01-B4.map'
    if os.path.exists(test_file):
        frame.file_paths = [test_file]
        frame.update_file_list()
        print(f"✅ Test file added: {test_file}")
    else:
        print(f"❌ Test file not found: {test_file}")
        return
    
    # Test different rotation angles
    rotation_tests = [
        (0, "Original (R0 suffix)"),
        (90, "90° Clockwise (R90 suffix)"),
        (180, "180° (R180 suffix)"),
        (270, "270° Counter-clockwise (R270 suffix)")
    ]
    
    print("\n📋 Instructions for manual testing:")
    print("1. The GUI window should open")
    print("2. Test file is already loaded")
    print("3. Try each rotation angle and click 'Process All Files'")
    print("4. Check the generated Excel filenames:")
    
    for angle, description in rotation_tests:
        print(f"   - {angle}°: Should generate filename with {description}")
    
    print("\n🎯 Expected filename patterns:")
    print("   - 0°:   3AA111_full_map_R0_YYYYMMDD_HHMMSS.xlsx")
    print("   - 90°:  3AA111_full_map_R90_YYYYMMDD_HHMMSS.xlsx")
    print("   - 180°: 3AA111_full_map_R180_YYYYMMDD_HHMMSS.xlsx")
    print("   - 270°: 3AA111_full_map_R270_YYYYMMDD_HHMMSS.xlsx")
    
    print("\n🚀 Starting GUI...")
    
    # Start GUI
    root.mainloop()

if __name__ == "__main__":
    test_gui_rotation_naming()
