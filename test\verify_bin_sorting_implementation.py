#!/usr/bin/env python3
"""
验证 bin 排序功能的正确实现
根据 worker_file4.txt 的要求验证功能
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tsk_map_processor import TSKMapProcessor
from full_map_processor import FullMapProcessor


def test_bin_sorting_requirements():
    """测试 bin 排序功能是否符合需求"""
    print("🎯 验证 bin 排序功能实现")
    print("=" * 60)
    print("📋 需求:")
    print("   • 默认打勾：按数量由大到小排列（现有行为）")
    print("   • 不打勾：按 bin 编号从 0 到 255 排列，无数据的 bin 显示 0")
    print("=" * 60)
    
    # 创建处理器实例
    processor = TSKMapProcessor()
    
    # 模拟测试数据
    processor.rowsize = 3
    processor.columnsize = 3
    processor.map_data = [
        [(1, 1), (2, 1), (0, 1)],  # bin1, bin2, bin0
        [(1, 1), (3, 1), (0, 1)],  # bin1, bin3, bin0  
        [(2, 1), (2, 1), (1, 1)]   # bin2, bin2, bin1
    ]
    
    print("📊 测试数据:")
    print("   bin0: 2 个, bin1: 3 个, bin2: 3 个, bin3: 1 个")
    print("   总测试数: 9")
    
    # 测试 1: 默认行为（按数量排序）
    print("\n1️⃣ 测试默认行为（打勾 - 按数量排序）...")
    bin_stats_qty = processor.get_bin_statistics(sort_by_quantity=True)
    
    print(f"   返回 {len(bin_stats_qty)} 个 bin（只有有数据的 bin）")
    print("   排序结果:")
    for i, stat in enumerate(bin_stats_qty, 1):
        print(f"   {i}. {stat['bin_name']}: {stat['quantity']} 个 ({stat['yield_percentage']:.2f}%)")
    
    # 验证按数量降序
    quantities = [stat['quantity'] for stat in bin_stats_qty]
    is_desc = all(quantities[i] >= quantities[i+1] for i in range(len(quantities)-1))
    print(f"   ✅ 按数量降序排列: {'是' if is_desc else '否'}")
    
    # 验证只包含有数据的 bin
    expected_bins = {'bin0', 'bin1', 'bin2', 'bin3'}
    actual_bins = {stat['bin_name'] for stat in bin_stats_qty}
    only_data_bins = actual_bins == expected_bins
    print(f"   ✅ 只包含有数据的 bin: {'是' if only_data_bins else '否'}")
    
    # 测试 2: 新功能（按编号排序）
    print("\n2️⃣ 测试新功能（不打勾 - 按编号排序）...")
    bin_stats_num = processor.get_bin_statistics(sort_by_quantity=False)
    
    print(f"   返回 {len(bin_stats_num)} 个 bin（bin0 到 bin255）")
    print("   前 10 个 bin:")
    for i in range(10):
        stat = bin_stats_num[i]
        print(f"   {i+1}. {stat['bin_name']}: {stat['quantity']} 个 ({stat['yield_percentage']:.2f}%)")
    
    # 验证总数为 256
    is_256_bins = len(bin_stats_num) == 256
    print(f"   ✅ 包含 256 个 bin (0-255): {'是' if is_256_bins else '否'}")
    
    # 验证按编号升序
    bin_numbers = [int(stat['bin_name'].replace('bin', '')) for stat in bin_stats_num]
    is_asc = bin_numbers == list(range(256))
    print(f"   ✅ 按编号升序排列 (0,1,2...255): {'是' if is_asc else '否'}")
    
    # 验证有数据的 bin 数量正确
    print("\n3️⃣ 验证有数据的 bin 数量...")
    expected_counts = {0: 2, 1: 3, 2: 3, 3: 1}
    all_correct = True
    for bin_num, expected_count in expected_counts.items():
        actual_count = bin_stats_num[bin_num]['quantity']
        is_correct = actual_count == expected_count
        print(f"   bin{bin_num}: 期望 {expected_count}, 实际 {actual_count} {'✅' if is_correct else '❌'}")
        if not is_correct:
            all_correct = False
    
    # 验证无数据的 bin 为 0
    print("\n4️⃣ 验证无数据的 bin 为 0...")
    zero_bins_sample = [4, 5, 10, 50, 100, 200, 255]
    all_zero = True
    for bin_num in zero_bins_sample:
        actual_count = bin_stats_num[bin_num]['quantity']
        is_zero = actual_count == 0
        print(f"   bin{bin_num}: {actual_count} {'✅' if is_zero else '❌'}")
        if not is_zero:
            all_zero = False
    
    # 验证总数量一致
    print("\n5️⃣ 验证总数量一致...")
    total_qty = sum(stat['quantity'] for stat in bin_stats_qty)
    total_num = sum(stat['quantity'] for stat in bin_stats_num)
    is_consistent = total_qty == total_num == 9
    print(f"   按数量排序总数: {total_qty}")
    print(f"   按编号排序总数: {total_num}")
    print(f"   期望总数: 9")
    print(f"   ✅ 总数一致: {'是' if is_consistent else '否'}")
    
    # 总结
    all_tests_pass = (is_desc and only_data_bins and is_256_bins and 
                     is_asc and all_correct and all_zero and is_consistent)
    
    print(f"\n📋 测试结果: {'全部通过' if all_tests_pass else '部分失败'}")
    return all_tests_pass


def test_integration():
    """测试集成功能"""
    print("\n" + "=" * 60)
    print("🔧 测试集成功能")
    print("=" * 60)
    
    try:
        # 测试 FullMapProcessor
        print("1️⃣ 测试 FullMapProcessor...")
        full_processor = FullMapProcessor()
        
        # 默认值
        default_ok = full_processor.sort_by_quantity == True
        print(f"   默认 sort_by_quantity: {full_processor.sort_by_quantity} {'✅' if default_ok else '❌'}")
        
        # 设置方法
        full_processor.set_sort_by_quantity(False)
        set_false_ok = full_processor.sort_by_quantity == False
        print(f"   设置为 False: {full_processor.sort_by_quantity} {'✅' if set_false_ok else '❌'}")
        
        full_processor.set_sort_by_quantity(True)
        set_true_ok = full_processor.sort_by_quantity == True
        print(f"   设置为 True: {full_processor.sort_by_quantity} {'✅' if set_true_ok else '❌'}")
        
        # 测试 GUI 集成
        print("\n2️⃣ 测试 GUI 集成...")
        import tkinter as tk
        from full_map_tool_frame import FullMapToolFrame
        
        root = tk.Tk()
        root.withdraw()
        
        class MockController:
            def __init__(self):
                self.root = root
        
        controller = MockController()
        frame = FullMapToolFrame(root, controller)
        
        # 默认值
        gui_default_ok = frame.sort_by_quantity.get() == True
        print(f"   GUI 默认值: {frame.sort_by_quantity.get()} {'✅' if gui_default_ok else '❌'}")
        
        # 设置值
        frame.sort_by_quantity.set(False)
        gui_false_ok = frame.sort_by_quantity.get() == False
        print(f"   GUI 设置为 False: {frame.sort_by_quantity.get()} {'✅' if gui_false_ok else '❌'}")
        
        root.destroy()
        
        integration_ok = (default_ok and set_false_ok and set_true_ok and 
                         gui_default_ok and gui_false_ok)
        
        print(f"\n📋 集成测试结果: {'全部通过' if integration_ok else '部分失败'}")
        return integration_ok
        
    except Exception as e:
        print(f"❌ 集成测试错误: {e}")
        return False


def main():
    """主函数"""
    print("🚀 Full Map Tool Bin 排序功能验证")
    print("根据 worker_file4.txt 要求验证实现")
    print("=" * 80)
    
    # 运行测试
    test1 = test_bin_sorting_requirements()
    test2 = test_integration()
    
    # 总结
    print("\n" + "=" * 80)
    print("📊 最终验证结果")
    print("=" * 80)
    print(f"   Bin 排序功能: {'通过' if test1 else '失败'}")
    print(f"   集成功能: {'通过' if test2 else '失败'}")
    
    all_passed = test1 and test2
    print(f"\n🎯 总体结果: {'所有功能正确实现' if all_passed else '部分功能需要修复'}")
    
    if all_passed:
        print("\n🎉 成功！Full Map Tool bin 排序功能已正确实现！")
        print("\n✅ 实现的功能:")
        print("   • 添加了排序选择复选框")
        print("   • 默认打勾：按数量由大到小排列（保持现有行为）")
        print("   • 不打勾：按 bin 编号从 0 到 255 排列")
        print("   • 无数据的 bin 显示数量为 0")
        print("   • 不破坏现有功能和良率统计")
        print("   • GUI 空间占用最小")
        print("   • 向后兼容")
        
        print("\n📝 技术实现:")
        print("   • 修改了 tsk_map_processor.py 的 get_bin_statistics 方法")
        print("   • 保持了 FullMapProcessor 和 GUI 的集成")
        print("   • 参数传递到 Excel 输出正确")
    else:
        print("\n❌ 部分功能实现有问题，请检查代码")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
