#!/usr/bin/env python3
"""
Test script for the enhanced Bin_Summary sheet formatting
Tests the beautiful styling and professional appearance
"""

import os
import sys
import tempfile

# Add parent directory to path to import modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_formatting_styles():
    """Test the formatting styles and color scheme"""
    print("🎨 Testing Bin_Summary Formatting Styles")
    print("=" * 50)
    
    try:
        from full_map_processor import FullMapProcessor
        from config_reader import ConfigReader
        from openpyxl import Workbook
        
        # Create processor with formatting capabilities
        processor = FullMapProcessor()
        
        # Test style setup
        print("1️⃣ Testing style setup...")
        processor._setup_summary_styles()
        
        # Verify style attributes exist
        required_attributes = ['colors', 'fonts', 'fills', 'borders', 'alignments']
        for attr in required_attributes:
            if hasattr(processor, attr):
                print(f"   ✅ {attr} defined")
            else:
                print(f"   ❌ {attr} missing")
                return False
        
        # Test color scheme
        print("2️⃣ Testing color scheme...")
        colors = processor.colors
        expected_colors = ['primary_blue', 'light_blue', 'accent_green', 'accent_red', 'neutral_gray']
        
        for color_name in expected_colors:
            if color_name in colors:
                print(f"   ✅ {color_name}: #{colors[color_name]}")
            else:
                print(f"   ❌ {color_name} missing")
                return False
        
        # Test font definitions
        print("3️⃣ Testing font definitions...")
        fonts = processor.fonts
        expected_fonts = ['header_large', 'header_medium', 'data_normal', 'yield_good', 'fail_count']
        
        for font_name in expected_fonts:
            if font_name in fonts:
                font = fonts[font_name]
                print(f"   ✅ {font_name}: {font.name}, size {font.size}, bold={font.bold}")
            else:
                print(f"   ❌ {font_name} missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error during style testing: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_header_formatting():
    """Test the header formatting functionality"""
    print("\n🎨 Testing Header Formatting")
    print("=" * 50)
    
    try:
        from full_map_processor import FullMapProcessor
        from config_reader import ConfigReader
        from openpyxl import Workbook
        
        # Create test setup
        processor = FullMapProcessor()
        
        # Create mock config
        config_reader = ConfigReader()
        config_reader.device_name = "TestChip_Pro_V3.0"
        config_reader.vendor_name = "TechCorp_Advanced"
        processor.set_config_reader(config_reader)
        
        # Create test workbook
        workbook = Workbook()
        worksheet = workbook.active
        worksheet.title = "Bin_Summary"
        
        # Create mock processors data
        mock_processors = {
            "BATCH2024_A-W01.map": {
                "processor": MockTSKProcessor(3000, 2850),  # 95% yield
                "file_path": "BATCH2024_A-W01.map"
            },
            "BATCH2024_A-W02.map": {
                "processor": MockTSKProcessor(2950, 2655),  # 90% yield
                "file_path": "BATCH2024_A-W02.map"
            },
            "BATCH2024_A-W03.map": {
                "processor": MockTSKProcessor(3100, 2170),  # 70% yield (poor)
                "file_path": "BATCH2024_A-W03.map"
            }
        }
        
        print("1️⃣ Testing header creation...")
        processor._create_summary_header(worksheet, mock_processors)
        
        # Verify header content and formatting
        print("2️⃣ Verifying header formatting...")
        
        # Check Row 1 formatting
        device_header = worksheet['A1']
        device_value = worksheet['C1']
        
        print(f"   Device Header: '{device_header.value}' - Font: {device_header.font.name}, Bold: {device_header.font.bold}")
        print(f"   Device Value: '{device_value.value}' - Font: {device_value.font.name}")
        
        # Check Row 2 formatting
        yield_header = worksheet['H2']
        yield_value = worksheet['I2']
        pass_value = worksheet['F2']
        fail_value = worksheet['K2']
        
        print(f"   Yield Header: '{yield_header.value}' - Font: {yield_header.font.name}")
        print(f"   Yield Value: '{yield_value.value}' - Color: {yield_value.font.color}")
        print(f"   Pass Value: '{pass_value.value}' - Color: {pass_value.font.color}")
        print(f"   Fail Value: '{fail_value.value}' - Color: {fail_value.font.color}")
        
        # Verify calculations
        expected_total = 3000 + 2950 + 3100  # 9050
        expected_pass = 2850 + 2655 + 2170   # 7675
        expected_yield = (expected_pass / expected_total * 100)  # ~84.81%
        
        actual_total = worksheet['C2'].value
        actual_pass = worksheet['F2'].value
        actual_yield_str = worksheet['I2'].value
        
        print(f"3️⃣ Verifying calculations...")
        print(f"   Total: Expected {expected_total:,}, Got {actual_total}")
        print(f"   Pass: Expected {expected_pass:,}, Got {actual_pass}")
        print(f"   Yield: Expected {expected_yield:.2f}%, Got {actual_yield_str}")
        
        # Check merged cells
        print("4️⃣ Verifying merged cells...")
        merged_ranges = list(worksheet.merged_cells.ranges)
        expected_merges = ['A1:B1', 'C1:D1', 'F1:G1', 'K1:L1', 'A2:B2', 'C2:D2', 'F2:G2', 'K2:L2']
        
        for expected_merge in expected_merges:
            found = any(str(merge) == expected_merge for merge in merged_ranges)
            if found:
                print(f"   ✅ {expected_merge} merged correctly")
            else:
                print(f"   ❌ {expected_merge} not merged")
                return False
        
        workbook.close()
        return True
        
    except Exception as e:
        print(f"❌ Error during header formatting test: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_table_formatting():
    """Test the bin statistics table formatting"""
    print("\n🎨 Testing Table Formatting")
    print("=" * 50)
    
    try:
        from full_map_processor import FullMapProcessor
        from openpyxl import Workbook
        
        # Create test setup
        processor = FullMapProcessor()
        processor._setup_summary_styles()
        
        # Create test workbook
        workbook = Workbook()
        worksheet = workbook.active
        
        # Test table headers formatting
        print("1️⃣ Testing table headers...")
        headers = ["LotID-waferID", "Yield(%)", "C00", "C01", "C02", "C03", "C04"]
        processor._format_bin_table_headers(worksheet, headers)
        
        # Verify header formatting
        for col_idx, header in enumerate(headers, 1):
            cell = worksheet.cell(row=5, column=col_idx)
            print(f"   Header '{header}': Font={cell.font.name}, Bold={cell.font.bold}, Color={cell.font.color}")
        
        # Test data row formatting
        print("2️⃣ Testing data row formatting...")
        
        # Add some test data
        test_data = [
            ["BATCH2024_A-W01", "95.00%", 0, 150, 0, 0, 5],
            ["BATCH2024_A-W02", "90.00%", 0, 295, 0, 0, 10],
            ["BATCH2024_A-W03", "70.00%", 0, 930, 0, 0, 25],
        ]
        
        for row_idx, row_data in enumerate(test_data, 6):
            for col_idx, value in enumerate(row_data, 1):
                worksheet.cell(row=row_idx, column=col_idx, value=value)
        
        # Apply data formatting
        processor._format_data_rows(worksheet, 6, 8)
        
        # Verify data formatting
        for row_idx in range(6, 9):
            yield_cell = worksheet.cell(row=row_idx, column=2)
            print(f"   Row {row_idx}: Yield={yield_cell.value}, Color={yield_cell.font.color}")
        
        # Test average row formatting
        print("3️⃣ Testing average row formatting...")
        avg_row = 10
        worksheet.cell(row=avg_row, column=1, value="Average")
        worksheet.cell(row=avg_row, column=2, value="85.00%")
        
        processor._format_average_row(worksheet, avg_row)
        
        avg_cell = worksheet.cell(row=avg_row, column=1)
        print(f"   Average row: Font={avg_cell.font.name}, Bold={avg_cell.font.bold}, BG={avg_cell.fill.start_color}")
        
        workbook.close()
        return True
        
    except Exception as e:
        print(f"❌ Error during table formatting test: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_visual_output():
    """Create a sample formatted Excel file for visual inspection"""
    print("\n🎨 Creating Sample Formatted Excel File")
    print("=" * 50)
    
    try:
        from full_map_processor import FullMapProcessor
        from config_reader import ConfigReader
        from openpyxl import Workbook
        
        # Create a complete formatted example
        processor = FullMapProcessor()
        
        # Setup config
        config_reader = ConfigReader()
        config_reader.device_name = "AdvancedChip_AI_V4.2"
        config_reader.vendor_name = "NextGen_Semiconductors"
        processor.set_config_reader(config_reader)
        
        # Create workbook
        workbook = Workbook()
        worksheet = workbook.active
        worksheet.title = "Bin_Summary"
        
        # Mock data
        mock_processors = {
            "LOT2024_PROD-W01-Site1.map": {"processor": MockTSKProcessor(4500, 4275), "file_path": "LOT2024_PROD-W01-Site1.map"},
            "LOT2024_PROD-W02-Site1.map": {"processor": MockTSKProcessor(4480, 4256), "file_path": "LOT2024_PROD-W02-Site1.map"},
            "LOT2024_PROD-W03-Site1.map": {"processor": MockTSKProcessor(4520, 4294), "file_path": "LOT2024_PROD-W03-Site1.map"},
            "LOT2024_PROD-W04-Site1.map": {"processor": MockTSKProcessor(4490, 4221), "file_path": "LOT2024_PROD-W04-Site1.map"},
            "LOT2024_PROD-W05-Site1.map": {"processor": MockTSKProcessor(4510, 4239), "file_path": "LOT2024_PROD-W05-Site1.map"},
        }
        
        # Create the formatted sheet
        processor._create_bin_summary_sheet(worksheet, mock_processors, workbook)
        
        # Save sample file
        sample_file = os.path.join(tempfile.gettempdir(), "Bin_Summary_Formatted_Sample.xlsx")
        workbook.save(sample_file)
        workbook.close()
        
        print(f"✅ Sample file created: {sample_file}")
        print("   Open this file to see the beautiful formatting!")
        print("   Features demonstrated:")
        print("   • Professional color scheme (blue/gray theme)")
        print("   • Segoe UI headers with Calibri data")
        print("   • Color-coded yield values (green/orange/red)")
        print("   • Merged cells for better layout")
        print("   • Alternating row colors")
        print("   • Professional borders and spacing")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating sample file: {e}")
        return False


class MockTSKProcessor:
    """Mock TSKMapProcessor for testing"""
    def __init__(self, total_tested, pass_count):
        self.total_tested = total_tested
        self.pass_count = pass_count
    
    def get_test_statistics(self):
        return {
            'total_tested': self.total_tested,
            'pass_count': self.pass_count,
            'yield_percentage': (self.pass_count / self.total_tested * 100) if self.total_tested > 0 else 0.0
        }


def main():
    """Run all formatting tests"""
    print("🎨 BIN_SUMMARY FORMATTING TEST SUITE")
    print("=" * 70)
    
    # Run tests
    test1 = test_formatting_styles()
    test2 = test_header_formatting()
    test3 = test_table_formatting()
    test4 = test_visual_output()
    
    # Summary
    print("\n" + "=" * 70)
    print("📋 FORMATTING TEST RESULTS")
    print("=" * 70)
    print(f"   Style Setup: {'PASS' if test1 else 'FAIL'}")
    print(f"   Header Formatting: {'PASS' if test2 else 'FAIL'}")
    print(f"   Table Formatting: {'PASS' if test3 else 'FAIL'}")
    print(f"   Visual Output: {'PASS' if test4 else 'FAIL'}")
    
    all_passed = test1 and test2 and test3 and test4
    print(f"\nOVERALL: {'ALL TESTS PASSED' if all_passed else 'SOME TESTS FAILED'}")
    
    if all_passed:
        print("\n🎉 SUCCESS! Bin_Summary formatting is beautiful and professional!")
        print("\n🎨 FORMATTING FEATURES:")
        print("   ✅ Professional blue and gray color scheme")
        print("   ✅ Segoe UI headers for modern look")
        print("   ✅ Calibri data fonts for readability")
        print("   ✅ Color-coded yield values (green/orange/red)")
        print("   ✅ Merged cells for clean layout")
        print("   ✅ Alternating row colors for easy reading")
        print("   ✅ Professional borders and spacing")
        print("   ✅ Optimal column widths and row heights")
    else:
        print("\n❌ Some formatting tests failed. Please check the implementation.")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
