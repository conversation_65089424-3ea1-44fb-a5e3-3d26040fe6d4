#!/usr/bin/env python3
"""
Final verification test for Full Map Tool interface clipping fix
Tests the actual user workflow through main application
"""

import os
import sys
import tkinter as tk

# Add parent directory to path to import modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_main_application_full_map():
    """Test Full Map Tool through main application (actual user workflow)"""
    print("Testing Full Map Tool through Main Application...")
    print("=" * 50)
    
    try:
        from main_application import TSKMapApplication
        
        # Create main application (simulates user starting the app)
        print("1. Starting main application...")
        app = TSKMapApplication()
        
        # Switch to Full Map Tool (simulates user selecting Full Map Tool)
        print("2. Switching to Full Map Tool...")
        app.show_full_map_tool()
        
        # Update layout to get accurate measurements
        app.root.update_idletasks()
        
        # Get window dimensions
        geometry = app.root.geometry()
        width = app.root.winfo_width()
        height = app.root.winfo_height()
        
        print(f"3. Window size: {width} x {height}")
        print(f"   Geometry: {geometry}")
        
        # Verify Full Map Tool is loaded
        if app.full_map_tool:
            print("4. ✓ Full Map Tool frame loaded successfully")
            
            # Check if the frame has all expected components
            frame = app.full_map_tool.main_frame
            if frame:
                print("5. ✓ Main frame created")
                
                # Check for key components
                components_found = []
                
                def check_components(widget, level=0):
                    """Recursively check for components"""
                    indent = "   " * level
                    widget_type = type(widget).__name__
                    
                    # Check for specific components
                    if hasattr(widget, 'cget'):
                        try:
                            text = widget.cget('text')
                            if 'Process All Files' in str(text):
                                components_found.append('Process All Files button')
                            elif 'Clear Memory' in str(text):
                                components_found.append('Clear Memory button')
                            elif 'Exit' in str(text):
                                components_found.append('Exit button')
                            elif 'Ready - Select MAP files' in str(text):
                                components_found.append('Status bar')
                        except:
                            pass
                    
                    # Recurse through children
                    try:
                        for child in widget.winfo_children():
                            check_components(child, level + 1)
                    except:
                        pass
                
                check_components(app.root)
                
                print("6. Components found:")
                for component in components_found:
                    print(f"   ✓ {component}")
                
                # Check if critical components are present
                critical_components = [
                    'Process All Files button',
                    'Clear Memory button', 
                    'Exit button',
                    'Status bar'
                ]
                
                missing_components = []
                for component in critical_components:
                    if component not in components_found:
                        missing_components.append(component)
                
                if missing_components:
                    print("7. ✗ Missing components:")
                    for component in missing_components:
                        print(f"   ✗ {component}")
                    success = False
                else:
                    print("7. ✓ All critical components found")
                    success = True
                
                # Check window height adequacy
                if height >= 920:
                    print(f"8. ✓ Window height ({height}) is adequate")
                    success = success and True
                else:
                    print(f"8. ✗ Window height ({height}) might be insufficient")
                    success = False
                
            else:
                print("5. ✗ Main frame not found")
                success = False
        else:
            print("4. ✗ Full Map Tool frame not loaded")
            success = False
        
        # Clean up
        app.root.destroy()
        
        return success
        
    except Exception as e:
        print(f"Error during test: {e}")
        return False


def test_direct_full_map_gui():
    """Test direct Full Map GUI (standalone mode)"""
    print("\nTesting Direct Full Map GUI...")
    print("=" * 50)
    
    try:
        from full_map_gui import FullMapGUI
        
        # Create standalone Full Map GUI
        print("1. Creating standalone Full Map GUI...")
        root = tk.Tk()
        app = FullMapGUI(root)
        
        # Update layout
        root.update_idletasks()
        
        # Get window dimensions
        geometry = root.geometry()
        width = root.winfo_width()
        height = root.winfo_height()
        
        print(f"2. Window size: {width} x {height}")
        print(f"   Geometry: {geometry}")
        
        # Check if height is adequate
        if height >= 920:
            print(f"3. ✓ Standalone window height ({height}) is adequate")
            success = True
        else:
            print(f"3. ✗ Standalone window height ({height}) might be insufficient")
            success = False
        
        # Clean up
        root.destroy()
        
        return success
        
    except Exception as e:
        print(f"Error during standalone test: {e}")
        return False


def test_comparison_with_ab_map():
    """Compare Full Map Tool size with AB Map Tool"""
    print("\nComparing with AB Map Tool...")
    print("=" * 50)
    
    try:
        from main_application import TSKMapApplication
        
        # Test AB Map Tool
        print("1. Testing AB Map Tool size...")
        app = TSKMapApplication()
        app.show_ab_map_tool()
        app.root.update_idletasks()
        
        ab_map_geometry = app.root.geometry()
        ab_map_width = app.root.winfo_width()
        ab_map_height = app.root.winfo_height()
        
        print(f"   AB Map Tool: {ab_map_width} x {ab_map_height}")
        
        # Test Full Map Tool
        print("2. Testing Full Map Tool size...")
        app.show_full_map_tool()
        app.root.update_idletasks()
        
        full_map_geometry = app.root.geometry()
        full_map_width = app.root.winfo_width()
        full_map_height = app.root.winfo_height()
        
        print(f"   Full Map Tool: {full_map_width} x {full_map_height}")
        
        # Compare
        width_diff = full_map_width - ab_map_width
        height_diff = full_map_height - ab_map_height
        
        print(f"3. Size difference: {width_diff:+d} x {height_diff:+d}")
        
        # Evaluate reasonableness
        if abs(height_diff) <= 150:  # Within reasonable range
            print("4. ✓ Size difference is reasonable")
            success = True
        else:
            print("4. ✗ Size difference might be too large")
            success = False
        
        app.root.destroy()
        return success
        
    except Exception as e:
        print(f"Error during comparison: {e}")
        return False


def main():
    """Run all final verification tests"""
    print("Full Map Tool Interface Fix - Final Verification")
    print("=" * 60)
    
    # Run tests
    test1 = test_main_application_full_map()
    test2 = test_direct_full_map_gui()
    test3 = test_comparison_with_ab_map()
    
    # Summary
    print("\n" + "=" * 60)
    print("FINAL TEST RESULTS:")
    print(f"   Main Application Integration: {'PASS' if test1 else 'FAIL'}")
    print(f"   Standalone GUI: {'PASS' if test2 else 'FAIL'}")
    print(f"   Size Comparison: {'PASS' if test3 else 'FAIL'}")
    
    all_passed = test1 and test2 and test3
    print(f"\nOVERALL RESULT: {'ALL TESTS PASSED' if all_passed else 'SOME TESTS FAILED'}")
    
    if all_passed:
        print("\n🎉 SUCCESS! Full Map Tool interface clipping issue is RESOLVED!")
        print("✓ All components are visible")
        print("✓ Window size is appropriate")
        print("✓ User experience is improved")
        print("\nUsers should no longer experience content clipping when using Full Map Tool.")
    else:
        print("\n❌ Some issues remain. Please check the failed tests above.")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
