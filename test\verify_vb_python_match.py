#!/usr/bin/env python3
"""
Verify VB-Python Match - Compare VB and Python implementations side by side
"""

import sys
import os
sys.path.append('..')
from tsk_map_processor import TSKMapProcessor


def analyze_vb_code_structure():
    """
    Analyze the VB code structure for GetDataFromDieResult_update
    """
    print("VB Code Structure Analysis")
    print("=" * 50)
    
    vb_code = """
    VB GetDataFromDieResult_update function:
    
    IsTestDie = False
    IsPassDie = False
    x = Binary2Val(Mid(Onedieresult, 8, 9))      // Position 8, Length 9
    y = Binary2Val(Mid(Onedieresult, 24, 9))     // Position 24, Length 9
    
    If Map_version = 0 Then
        Category = Binary2Val(Mid(Onedieresult, 43, 6))     // Position 43, Length 6
    ElseIf Map_version = 2 Or Map_version = 3 Then
        Category = Binary2Val(Mid(OnedieresultCategory, 1, 16))   // Position 1, Length 16
    Else
        Category = Binary2Val(Mid(OnedieresultCategory, 23, 10))  // Position 23, Length 10
    End If
    
    If (Binary2Val(Mid(Onedieresult, 17, 2)) = 1) Then IsTestDie = True    // Position 17, Length 2
    If (Binary2Val(Mid(Onedieresult, 1, 2)) = 1) Then IsPassDie = True     // Position 1, Length 2
    """
    
    print(vb_code)
    
    print("\nVB Mid() to Python slice mapping:")
    mappings = [
        ("IsPassDie", "Mid(Onedieresult, 1, 2)", "[0:2]", "Bits 1-2 -> 0-1"),
        ("X coordinate", "Mid(Onedieresult, 8, 9)", "[7:16]", "Bits 8-16 -> 7-15"),
        ("IsTestDie", "Mid(Onedieresult, 17, 2)", "[16:18]", "Bits 17-18 -> 16-17"),
        ("Y coordinate", "Mid(Onedieresult, 24, 9)", "[23:32]", "Bits 24-32 -> 23-31"),
        ("Category v0", "Mid(Onedieresult, 43, 6)", "[42:48]", "Bits 43-48 -> 42-47"),
        ("Category v2/3", "Mid(OnedieresultCategory, 1, 16)", "[0:16]", "Bits 1-16 -> 0-15"),
        ("Category other", "Mid(OnedieresultCategory, 23, 10)", "[22:32]", "Bits 23-32 -> 22-31"),
    ]
    
    for field, vb_syntax, py_syntax, conversion in mappings:
        print(f"  {field:15} | {vb_syntax:30} | {py_syntax:8} | {conversion}")


def compare_implementations(filepath):
    """
    Compare VB and Python implementations with real data
    """
    print(f"\nImplementation Comparison with Real Data: {os.path.basename(filepath)}")
    print("=" * 70)
    
    processor = TSKMapProcessor()
    
    if not processor.read_file(filepath):
        print("❌ Failed to read file")
        return False
    
    if not processor.parse_file_header():
        print("❌ Failed to parse header")
        return False
    
    print(f"File info: {processor.columnsize}x{processor.rowsize}, Version {processor.May_version}")
    
    # Test several dies to verify consistency
    test_dies = [0, 1, 2, 10, 20]  # Different die indices
    
    for die_idx in test_dies:
        if die_idx >= processor.rowsize * processor.columnsize:
            continue
            
        i = (die_idx // processor.columnsize) + 1
        j = (die_idx % processor.columnsize) + 1
        
        # Get die data (matching VB calculation exactly)
        result_start = processor.TestResultStartPos + 6 * die_idx + 1
        result_end = result_start + 5
        
        print(f"\nDie ({i},{j}) - Index {die_idx}:")
        
        if result_end < len(processor.filearray):
            onedieresult = processor.get_binary(processor.filearray, result_start, result_end + 1)
            
            # Get category data if needed
            category_start_pos = processor.TestResultCategory + 4 * die_idx
            onedieresult_category = ""
            if category_start_pos + 3 < len(processor.filearray):
                onedieresult_category = processor.get_binary(processor.filearray, 
                                                           category_start_pos, 
                                                           category_start_pos + 3)
            
            print(f"  Die result binary ({len(onedieresult)} bits): {onedieresult[:40]}...")
            if onedieresult_category:
                print(f"  Category binary ({len(onedieresult_category)} bits): {onedieresult_category}")
            
            # Manual extraction (simulating VB code exactly)
            print(f"  Manual field extraction:")
            
            # VB: x = Binary2Val(Mid(Onedieresult, 8, 9))
            if len(onedieresult) >= 16:
                x_manual = processor.binary2val(onedieresult[7:16])
                print(f"    X [7:16]: {onedieresult[7:16]} = {x_manual}")
            else:
                x_manual = 0
                print(f"    X: INSUFFICIENT DATA")
            
            # VB: y = Binary2Val(Mid(Onedieresult, 24, 9))
            if len(onedieresult) >= 32:
                y_manual = processor.binary2val(onedieresult[23:32])
                print(f"    Y [23:32]: {onedieresult[23:32]} = {y_manual}")
            else:
                y_manual = 0
                print(f"    Y: INSUFFICIENT DATA")
            
            # VB: If (Binary2Val(Mid(Onedieresult, 17, 2)) = 1) Then IsTestDie = True
            if len(onedieresult) >= 18:
                test_manual = processor.binary2val(onedieresult[16:18]) == 1
                print(f"    IsTestDie [16:18]: {onedieresult[16:18]} = {test_manual}")
            else:
                test_manual = False
                print(f"    IsTestDie: INSUFFICIENT DATA")
            
            # VB: If (Binary2Val(Mid(Onedieresult, 1, 2)) = 1) Then IsPassDie = True
            if len(onedieresult) >= 2:
                pass_manual = processor.binary2val(onedieresult[0:2]) == 1
                print(f"    IsPassDie [0:2]: {onedieresult[0:2]} = {pass_manual}")
            else:
                pass_manual = False
                print(f"    IsPassDie: INSUFFICIENT DATA")
            
            # Category extraction
            category_manual = 0
            if processor.May_version == 0:
                if len(onedieresult) >= 48:
                    category_manual = processor.binary2val(onedieresult[42:48])
                    print(f"    Category v0 [42:48]: {onedieresult[42:48]} = {category_manual}")
            elif processor.May_version == 2 or processor.May_version == 3:
                if len(onedieresult_category) >= 16:
                    category_manual = processor.binary2val(onedieresult_category[0:16])
                    print(f"    Category v2/3 [0:16]: {onedieresult_category[0:16]} = {category_manual}")
            else:
                if len(onedieresult_category) >= 32:
                    category_manual = processor.binary2val(onedieresult_category[22:32])
                    print(f"    Category other [22:32]: {onedieresult_category[22:32]} = {category_manual}")
            
            # Use Python function
            x_func, y_func, cat_func, test_func, pass_func = processor.get_data_from_die_result_update(
                processor.May_version, onedieresult, onedieresult_category)
            
            print(f"  Python function results:")
            print(f"    x={x_func}, y={y_func}, category={cat_func}")
            print(f"    is_test_die={test_func}, is_pass_die={pass_func}")
            
            # Compare results
            print(f"  Comparison:")
            x_match = x_manual == x_func
            y_match = y_manual == y_func
            cat_match = category_manual == cat_func
            test_match = test_manual == test_func
            pass_match = pass_manual == pass_func
            
            print(f"    X: {'✅' if x_match else '❌'} ({x_manual} vs {x_func})")
            print(f"    Y: {'✅' if y_match else '❌'} ({y_manual} vs {y_func})")
            print(f"    Category: {'✅' if cat_match else '❌'} ({category_manual} vs {cat_func})")
            print(f"    IsTestDie: {'✅' if test_match else '❌'} ({test_manual} vs {test_func})")
            print(f"    IsPassDie: {'✅' if pass_match else '❌'} ({pass_manual} vs {pass_func})")
            
            all_match = x_match and y_match and cat_match and test_match and pass_match
            print(f"    Overall: {'✅ PERFECT MATCH' if all_match else '❌ MISMATCH'}")


def main():
    """
    Main verification function
    """
    if len(sys.argv) < 2:
        print("VB-Python Implementation Verification")
        print("Usage: python verify_vb_python_match.py <tsk_file_path>")
        print("Example: python verify_vb_python_match.py ../3AA111-01-B4")
        return
    
    filepath = sys.argv[1]
    
    print("VB-Python Implementation Verification")
    print("Ensuring Python matches VB GetDataFromDieResult_update exactly")
    print("=" * 80)
    
    # Analyze VB code structure
    analyze_vb_code_structure()
    
    # Compare implementations
    compare_implementations(filepath)
    
    print("\n" + "=" * 80)
    print("Verification completed!")
    print("If all comparisons show ✅ PERFECT MATCH, the implementations are identical.")


if __name__ == "__main__":
    main()
