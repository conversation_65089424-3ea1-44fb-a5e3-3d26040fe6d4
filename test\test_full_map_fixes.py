#!/usr/bin/env python3
"""
Test Full Map Fixes - Verify B10 percentage formatting and filename generation
"""

import sys
import os
import shutil
import re
from datetime import datetime

# Add parent directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

from full_map_processor import FullMapProcessor
from config_reader import ConfigReader

try:
    from openpyxl import load_workbook
except ImportError:
    print("Error: openpyxl not installed. Please run: pip install openpyxl")
    exit(1)


def create_test_files_with_names():
    """Create test files with specific names for filename testing"""
    original_file = os.path.join(parent_dir, "3AA111-01-B4")
    
    if not os.path.exists(original_file):
        print(f"❌ Original file not found: {original_file}")
        return []
    
    test_files = []
    test_names = [
        "3AA111-01-B4.map",  # First file for filename testing
        "5BB222-02-C5.map",  # Second file
        "7CC333-03-D6.map"   # Third file
    ]
    
    for test_name in test_names:
        test_path = os.path.join(current_dir, test_name)
        try:
            shutil.copy2(original_file, test_path)
            test_files.append(test_path)
            print(f"✅ Created test file: {test_name}")
        except Exception as e:
            print(f"❌ Failed to create {test_name}: {e}")
    
    return test_files


def test_b10_percentage_formatting():
    """Test B10 percentage formatting in all sheets"""
    print("Testing B10 Percentage Formatting")
    print("=" * 50)
    
    # Create test files
    test_files = create_test_files_with_names()
    
    if not test_files:
        print("❌ No test files available")
        return False
    
    try:
        # Create processor
        processor = FullMapProcessor()
        processor.set_rotation_angle(0)
        processor.set_filter_empty(True)
        
        # Load config file if available
        config_path = os.path.join(parent_dir, "3509_CP1_program_bin.xlsx")
        if os.path.exists(config_path):
            config_reader = ConfigReader()
            if config_reader.read_config_file(config_path):
                processor.set_config_reader(config_reader)
        
        # Process files
        print(f"Processing {len(test_files)} test files...")
        output_file = processor.process_multiple_files(test_files)
        
        if not output_file or not os.path.exists(output_file):
            print("❌ Failed to create output file")
            return False
        
        print(f"✅ Output file created: {os.path.basename(output_file)}")
        
        # Verify B10 formatting in all sheets
        workbook = load_workbook(output_file)
        
        print(f"\nChecking B10 formatting in {len(workbook.sheetnames)} sheets:")
        
        all_sheets_correct = True
        
        for i, sheet_name in enumerate(workbook.sheetnames, 1):
            worksheet = workbook[sheet_name]
            b10_cell = worksheet['B10']
            
            print(f"  Sheet {i} ({sheet_name}):")
            print(f"    B10 value: {b10_cell.value}")
            print(f"    B10 number format: '{b10_cell.number_format}'")
            
            # Check if it's a percentage format
            is_percentage_format = (
                b10_cell.number_format == "0.00%" or 
                "%" in b10_cell.number_format
            )
            
            # Check if value is decimal (for percentage)
            is_decimal_value = isinstance(b10_cell.value, (int, float)) and 0 <= b10_cell.value <= 1
            
            if is_percentage_format and is_decimal_value:
                print(f"    ✅ Correct percentage formatting")
            else:
                print(f"    ❌ Incorrect formatting")
                print(f"       Expected: decimal value (0-1) with '0.00%' format")
                print(f"       Got: value={b10_cell.value}, format='{b10_cell.number_format}'")
                all_sheets_correct = False
        
        workbook.close()
        
        # Move output file to test directory
        test_output = os.path.join(current_dir, os.path.basename(output_file))
        if output_file != test_output:
            os.rename(output_file, test_output)
            print(f"  Moved to test directory: {os.path.basename(test_output)}")
        
        return all_sheets_correct
        
    except Exception as e:
        print(f"❌ Error in B10 formatting test: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Clean up test files
        for test_file in test_files:
            try:
                if os.path.exists(test_file):
                    os.remove(test_file)
            except:
                pass


def test_filename_generation():
    """Test filename generation with timestamp"""
    print(f"\nTesting Filename Generation")
    print("=" * 40)
    
    # Create test files with specific names
    test_files = create_test_files_with_names()
    
    if not test_files:
        print("❌ No test files available")
        return False
    
    try:
        # Test multiple files
        processor = FullMapProcessor()
        processor.set_rotation_angle(0)
        processor.set_filter_empty(True)
        
        print(f"Processing files for filename test:")
        for i, file_path in enumerate(test_files, 1):
            filename = os.path.basename(file_path)
            print(f"  {i}. {filename}")
        
        # Record time before processing
        start_time = datetime.now()
        
        output_file = processor.process_multiple_files(test_files)
        
        if not output_file:
            print("❌ Failed to create output file")
            return False
        
        output_filename = os.path.basename(output_file)
        print(f"\nGenerated filename: {output_filename}")
        
        # Verify filename format
        # Expected format: 3AA111_all_maps_summary_YYYYMMDD_HHMMSS.xlsx
        expected_pattern = r"^3AA111_all_maps_summary_\d{8}_\d{6}\.xlsx$"
        
        if re.match(expected_pattern, output_filename):
            print(f"✅ Filename format is correct")
            
            # Extract timestamp from filename
            timestamp_match = re.search(r"(\d{8}_\d{6})", output_filename)
            if timestamp_match:
                timestamp_str = timestamp_match.group(1)
                try:
                    file_timestamp = datetime.strptime(timestamp_str, "%Y%m%d_%H%M%S")
                    
                    # Check if timestamp is reasonable (within last few minutes)
                    time_diff = abs((file_timestamp - start_time).total_seconds())
                    if time_diff < 300:  # Within 5 minutes
                        print(f"✅ Timestamp is current: {timestamp_str}")
                    else:
                        print(f"⚠️  Timestamp seems old: {timestamp_str}")
                        
                except ValueError:
                    print(f"❌ Invalid timestamp format: {timestamp_str}")
                    return False
        else:
            print(f"❌ Filename format is incorrect")
            print(f"   Expected pattern: 3AA111_all_maps_summary_YYYYMMDD_HHMMSS.xlsx")
            print(f"   Got: {output_filename}")
            return False
        
        # Test single file naming
        print(f"\nTesting single file naming:")
        single_output = processor.process_multiple_files([test_files[0]])
        
        if single_output:
            single_filename = os.path.basename(single_output)
            print(f"Single file output: {single_filename}")
            
            # Expected format: 3AA111_full_map_YYYYMMDD_HHMMSS.xlsx
            single_pattern = r"^3AA111_full_map_\d{8}_\d{6}\.xlsx$"
            
            if re.match(single_pattern, single_filename):
                print(f"✅ Single file naming is correct")
            else:
                print(f"❌ Single file naming is incorrect")
                return False
            
            # Move to test directory
            test_single = os.path.join(current_dir, single_filename)
            if single_output != test_single:
                os.rename(single_output, test_single)
        
        # Move main output to test directory
        test_output = os.path.join(current_dir, output_filename)
        if output_file != test_output:
            os.rename(output_file, test_output)
        
        return True
        
    except Exception as e:
        print(f"❌ Error in filename generation test: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Clean up test files
        for test_file in test_files:
            try:
                if os.path.exists(test_file):
                    os.remove(test_file)
            except:
                pass


def test_edge_cases():
    """Test edge cases for filename generation"""
    print(f"\nTesting Edge Cases")
    print("=" * 30)
    
    try:
        processor = FullMapProcessor()
        
        # Test with empty file list
        empty_filename = processor._generate_output_filename([], 0)
        print(f"Empty file list: {empty_filename}")
        
        # Test with file without dash
        no_dash_filename = processor._generate_output_filename(["testfile.map"], 1)
        print(f"No dash in filename: {no_dash_filename}")
        
        # Test with complex filename
        complex_filename = processor._generate_output_filename(["ABC-DEF-GHI-123.map"], 3)
        print(f"Complex filename: {complex_filename}")
        
        # Verify all have timestamps
        timestamp_pattern = r"\d{8}_\d{6}"
        
        test_cases = [empty_filename, no_dash_filename, complex_filename]
        all_have_timestamps = all(re.search(timestamp_pattern, filename) for filename in test_cases)
        
        if all_have_timestamps:
            print(f"✅ All edge cases have timestamps")
            return True
        else:
            print(f"❌ Some edge cases missing timestamps")
            return False
            
    except Exception as e:
        print(f"❌ Error in edge cases test: {e}")
        return False


def main():
    """Main test function"""
    print("TSK/MAP Full Map Fixes Test")
    print("Testing B10 percentage formatting and filename generation")
    print("=" * 70)
    
    success_count = 0
    total_tests = 3
    
    # Test 1: B10 percentage formatting
    if test_b10_percentage_formatting():
        success_count += 1
        print("✅ Test 1 passed: B10 percentage formatting")
    else:
        print("❌ Test 1 failed: B10 percentage formatting")
    
    # Test 2: Filename generation
    if test_filename_generation():
        success_count += 1
        print("✅ Test 2 passed: Filename generation")
    else:
        print("❌ Test 2 failed: Filename generation")
    
    # Test 3: Edge cases
    if test_edge_cases():
        success_count += 1
        print("✅ Test 3 passed: Edge cases")
    else:
        print("❌ Test 3 failed: Edge cases")
    
    print("\n" + "=" * 70)
    print(f"Full Map Fixes Test Results: {success_count}/{total_tests} tests passed")
    
    if success_count == total_tests:
        print("🎉 All Full Map fixes verified!")
        print("\nFixed Issues:")
        print("✅ B10 percentage formatting in all sheets")
        print("✅ Filename generation with timestamp")
        print("✅ Base name extraction from first file")
        print("✅ Proper decimal to percentage conversion")
        
        print(f"\nFilename Format:")
        print(f"• Multiple files: [base]_all_maps_summary_YYYYMMDD_HHMMSS.xlsx")
        print(f"• Single file: [base]_full_map_YYYYMMDD_HHMMSS.xlsx")
        print(f"• Base name: extracted from first file before first '-'")
        print(f"• Example: 3AA111-01-B4 → 3AA111_all_maps_summary_20241207_143022.xlsx")
        
        print(f"\nB10 Formatting:")
        print(f"• All sheets have consistent percentage formatting")
        print(f"• Values stored as decimals (0.9293 for 92.93%)")
        print(f"• Display format: '0.00%' with 2 decimal places")
        print(f"• Center alignment maintained")
    else:
        print(f"⚠️  {total_tests - success_count} tests failed")


if __name__ == "__main__":
    main()
