#!/usr/bin/env python3
"""
Test Full Map Tool - Verify the full map processing functionality
"""

import sys
import os
import shutil

# Add parent directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

from full_map_processor import FullMapProcessor
from config_reader import Config<PERSON>eader


def create_test_map_files():
    """Create test MAP files by copying the original file"""
    original_file = os.path.join(parent_dir, "3AA111-01-B4")
    
    if not os.path.exists(original_file):
        print(f"❌ Original file not found: {original_file}")
        return []
    
    test_files = []
    test_names = ["TestMap1.map", "TestMap2.map", "TestMap3.map"]
    
    for test_name in test_names:
        test_path = os.path.join(current_dir, test_name)
        try:
            shutil.copy2(original_file, test_path)
            test_files.append(test_path)
            print(f"✅ Created test file: {test_name}")
        except Exception as e:
            print(f"❌ Failed to create {test_name}: {e}")
    
    return test_files


def test_full_map_processor():
    """Test the FullMapProcessor functionality"""
    print("Testing Full Map Processor")
    print("=" * 50)
    
    # Create test files
    test_files = create_test_map_files()
    
    if not test_files:
        print("❌ No test files available")
        return False
    
    try:
        # Create processor
        processor = FullMapProcessor()
        
        # Set options
        processor.set_rotation_angle(0)
        processor.set_filter_empty(True)
        
        # Load config file if available
        config_path = os.path.join(parent_dir, "3509_CP1_program_bin.xlsx")
        if os.path.exists(config_path):
            config_reader = ConfigReader()
            if config_reader.read_config_file(config_path):
                processor.set_config_reader(config_reader)
                print(f"✅ Configuration loaded: {len(config_reader.get_all_bin_mappings())} bin mappings")
        
        # Process files
        print(f"\nProcessing {len(test_files)} test files...")
        output_file = processor.process_multiple_files(test_files)
        
        if output_file:
            print(f"✅ Processing successful!")
            print(f"  Output file: {output_file}")
            
            # Verify output file
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                print(f"  File size: {file_size:,} bytes")
                
                # Try to open and verify sheets
                try:
                    from openpyxl import load_workbook
                    workbook = load_workbook(output_file)
                    sheet_names = workbook.sheetnames
                    
                    print(f"  Sheets created: {len(sheet_names)}")
                    for i, sheet_name in enumerate(sheet_names, 1):
                        print(f"    {i}. {sheet_name}")
                    
                    # Verify each sheet has data
                    for sheet_name in sheet_names:
                        worksheet = workbook[sheet_name]
                        max_row = worksheet.max_row
                        max_col = worksheet.max_column
                        print(f"    Sheet '{sheet_name}': {max_row} rows, {max_col} columns")
                    
                    workbook.close()
                    print(f"✅ Excel file verification successful")
                    
                except Exception as e:
                    print(f"⚠️  Excel file verification failed: {e}")
                
                return True
            else:
                print(f"❌ Output file not found: {output_file}")
                return False
        else:
            print("❌ Processing failed")
            return False
            
    except Exception as e:
        print(f"❌ Error in full map processor test: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Clean up test files
        for test_file in test_files:
            try:
                if os.path.exists(test_file):
                    os.remove(test_file)
                    print(f"🧹 Cleaned up: {os.path.basename(test_file)}")
            except:
                pass


def test_rotation_angles():
    """Test different rotation angles"""
    print(f"\nTesting Different Rotation Angles")
    print("=" * 50)
    
    # Create test files
    test_files = create_test_map_files()
    
    if not test_files:
        print("❌ No test files available")
        return False
    
    # Test each rotation angle
    angles = [0, 90, 180, 270]
    success_count = 0
    
    for angle in angles:
        print(f"\nTesting rotation angle: {angle}°")
        
        try:
            processor = FullMapProcessor()
            processor.set_rotation_angle(angle)
            processor.set_filter_empty(True)
            
            # Use only first test file for rotation test
            output_file = processor.process_multiple_files([test_files[0]])
            
            if output_file:
                # Rename output file to include angle
                angle_output = os.path.join(current_dir, f"test_rotation_{angle}_degrees.xlsx")
                if os.path.exists(output_file):
                    os.rename(output_file, angle_output)
                    print(f"✅ Rotation {angle}° successful: {os.path.basename(angle_output)}")
                    success_count += 1
                else:
                    print(f"❌ Rotation {angle}° failed: output file not found")
            else:
                print(f"❌ Rotation {angle}° failed: processing error")
                
        except Exception as e:
            print(f"❌ Rotation {angle}° failed: {e}")
    
    # Clean up test files
    for test_file in test_files:
        try:
            if os.path.exists(test_file):
                os.remove(test_file)
        except:
            pass
    
    print(f"\nRotation test summary: {success_count}/{len(angles)} angles successful")
    return success_count == len(angles)


def test_single_file_processing():
    """Test processing with single file"""
    print(f"\nTesting Single File Processing")
    print("=" * 40)
    
    original_file = os.path.join(parent_dir, "3AA111-01-B4")
    
    if not os.path.exists(original_file):
        print(f"❌ Original file not found: {original_file}")
        return False
    
    try:
        processor = FullMapProcessor()
        processor.set_rotation_angle(0)
        processor.set_filter_empty(True)
        
        output_file = processor.process_multiple_files([original_file])
        
        if output_file:
            print(f"✅ Single file processing successful")
            print(f"  Output: {output_file}")
            
            # Move to test directory
            test_output = os.path.join(current_dir, "test_single_file.xlsx")
            if os.path.exists(output_file):
                os.rename(output_file, test_output)
                print(f"  Moved to: {os.path.basename(test_output)}")
            
            return True
        else:
            print("❌ Single file processing failed")
            return False
            
    except Exception as e:
        print(f"❌ Single file processing error: {e}")
        return False


def main():
    """Main test function"""
    print("TSK/MAP Full Map Tool Test")
    print("Testing multiple MAP file processing functionality")
    print("=" * 70)
    
    success_count = 0
    total_tests = 3
    
    # Test 1: Full map processor
    if test_full_map_processor():
        success_count += 1
        print("✅ Test 1 passed: Full map processor")
    else:
        print("❌ Test 1 failed: Full map processor")
    
    # Test 2: Rotation angles
    if test_rotation_angles():
        success_count += 1
        print("✅ Test 2 passed: Rotation angles")
    else:
        print("❌ Test 2 failed: Rotation angles")
    
    # Test 3: Single file processing
    if test_single_file_processing():
        success_count += 1
        print("✅ Test 3 passed: Single file processing")
    else:
        print("❌ Test 3 failed: Single file processing")
    
    print("\n" + "=" * 70)
    print(f"Full Map Tool Test Results: {success_count}/{total_tests} tests passed")
    
    if success_count == total_tests:
        print("🎉 All Full Map Tool tests passed!")
        print("\nImplemented Features:")
        print("✅ Multiple MAP file processing")
        print("✅ Single Excel file with multiple sheets")
        print("✅ Sheet naming based on MAP file names")
        print("✅ Configuration file support")
        print("✅ Rotation angle support (0°, 90°, 180°, 270°)")
        print("✅ Code reuse from AB Map Tool")
        print("✅ Bold bin name formatting")
        
        print(f"\nArchitecture:")
        print(f"• ToolSelector: Choose between AB Map and Full Map tools")
        print(f"• FullMapGUI: User interface for multiple file selection")
        print(f"• FullMapProcessor: Core processing logic (reuses TSKMapProcessor)")
        print(f"• ExcelOutputHandler: Reused for sheet creation")
        print(f"• ConfigReader: Reused for bin name mapping")
        
        print(f"\nTest Files Generated:")
        print(f"• test/all_maps_summary.xlsx (multiple files)")
        print(f"• test/test_rotation_*_degrees.xlsx (rotation tests)")
        print(f"• test/test_single_file.xlsx (single file test)")
    else:
        print(f"⚠️  {total_tests - success_count} tests failed")


if __name__ == "__main__":
    main()
