#!/usr/bin/env python3
"""
Test script for TSK/MAP File Processor Tool
Tests both AB Map Tool and Full Map Tool functionality
"""

import sys
import os
import tkinter as tk
from pathlib import Path

# Add parent directory to path to import modules
sys.path.insert(0, str(Path(__file__).parent.parent))

def test_ab_map_tool():
    """Test AB Map Tool functionality"""
    print("=== Testing AB Map Tool ===")
    
    try:
        from main_application import TSKMapApplication
        
        app = TSKMapApplication()
        app.root.withdraw()  # Hide window for testing
        
        print("✅ Main application created")
        
        # Switch to AB Map Tool
        app.show_ab_map_tool()
        print("✅ AB Map Tool loaded")
        
        # Test the three modes
        ab_tool = app.ab_map_tool.ab_map_gui
        
        # Test Amap mode
        ab_tool.mode_selection.set("Amap")
        print(f"✅ Amap mode selected: {ab_tool.mode_selection.get()}")
        
        # Test Bmap mode
        ab_tool.mode_selection.set("Bmap")
        print(f"✅ Bmap mode selected: {ab_tool.mode_selection.get()}")
        
        # Test AB Compare mode
        ab_tool.mode_selection.set("AB Compare")
        print(f"✅ AB Compare mode selected: {ab_tool.mode_selection.get()}")
        
        # Test filter option (should be checked by default)
        filter_enabled = ab_tool.filter_empty.get()
        print(f"✅ Filter empty areas enabled: {filter_enabled}")
        
        # Test return functionality
        print("Testing return to main menu...")
        app.ab_map_tool.return_to_selector()
        
        app.root.destroy()
        print("✅ AB Map Tool test completed")
        
    except Exception as e:
        print(f"❌ AB Map Tool test failed: {e}")
        import traceback
        traceback.print_exc()

def test_full_map_tool():
    """Test Full Map Tool functionality"""
    print("\n=== Testing Full Map Tool ===")

    try:
        from main_application import TSKMapApplication

        app = TSKMapApplication()
        app.root.withdraw()  # Hide window for testing

        print("✅ Main application created")

        # Switch to Full Map Tool
        app.show_full_map_tool()
        print("✅ Full Map Tool loaded")

        full_tool = app.full_map_tool

        # Test adding test MAP file
        test_map_file = os.path.join(os.path.dirname(__file__), "3AA111-01-B4.map")
        if os.path.exists(test_map_file):
            full_tool.map_files = [test_map_file]
            full_tool.update_info_display()
            print(f"✅ Test MAP file added: {os.path.basename(test_map_file)}")

            # Test process_all_files method (configuration handling)
            print("Testing ConfigReader initialization...")
            from config_reader import ConfigReader
            config_reader = ConfigReader()
            print("✅ ConfigReader created successfully")

            # Test the process_all_files method structure
            print("Testing process_all_files method...")
            if hasattr(full_tool, 'process_all_files'):
                print("✅ process_all_files method exists")
            else:
                print("❌ process_all_files method missing")

        else:
            print(f"⚠️ Test MAP file not found: {test_map_file}")

        # Test filter option (should be checked by default)
        filter_enabled = full_tool.filter_empty.get()
        print(f"✅ Filter empty areas enabled: {filter_enabled}")

        # Test return functionality
        print("Testing return to main menu...")
        app.full_map_tool.return_to_selector()

        app.root.destroy()
        print("✅ Full Map Tool test completed")

    except Exception as e:
        print(f"❌ Full Map Tool test failed: {e}")
        import traceback
        traceback.print_exc()

def test_memory_cleanup():
    """Test memory cleanup functionality"""
    print("\n=== Testing Memory Cleanup ===")
    
    try:
        from main_application import TSKMapApplication
        
        app = TSKMapApplication()
        app.root.withdraw()  # Hide window for testing
        
        print("✅ Main application created")
        
        # Test AB Map Tool memory cleanup
        app.show_ab_map_tool()
        ab_tool = app.ab_map_tool
        
        # Simulate memory usage
        print("Testing AB Map Tool memory cleanup...")
        ab_tool.ab_map_gui.clear_memory()
        print("✅ AB Map Tool memory cleanup tested")
        
        # Test Full Map Tool memory cleanup
        app.show_full_map_tool()
        full_tool = app.full_map_tool
        
        print("Testing Full Map Tool memory cleanup...")
        full_tool.clear_memory()
        print("✅ Full Map Tool memory cleanup tested")
        
        app.root.destroy()
        print("✅ Memory cleanup test completed")
        
    except Exception as e:
        print(f"❌ Memory cleanup test failed: {e}")
        import traceback
        traceback.print_exc()

def test_file_processing():
    """Test actual file processing with test MAP file"""
    print("\n=== Testing File Processing ===")
    
    test_map_file = os.path.join(os.path.dirname(__file__), "3AA111-01-B4.map")
    if not os.path.exists(test_map_file):
        print(f"⚠️ Test MAP file not found: {test_map_file}")
        print("Skipping file processing test")
        return
    
    try:
        # Test TSKMapProcessor directly
        sys.path.insert(0, str(Path(__file__).parent.parent))
        from tsk_map_processor import TSKMapProcessor
        
        processor = TSKMapProcessor()
        
        print(f"Testing file reading: {os.path.basename(test_map_file)}")
        success = processor.read_file(test_map_file)
        
        if success:
            print("✅ File read successfully")
            
            # Get file info
            info = processor.get_file_info()
            if info:
                print(f"✅ File info: Size: {info.get('filesize', 'Unknown')} bytes")
                print(f"   Dimensions: {info.get('columnsize', 'Unknown')} x {info.get('rowsize', 'Unknown')}")
                print(f"   Version: {info.get('version', 'Unknown')}")
            
            # Test memory usage
            memory_usage = processor.get_memory_usage_mb()
            print(f"✅ Memory usage: {memory_usage:.1f} MB")
            
            # Test memory cleanup
            processor.clear_memory()
            print("✅ Memory cleared")
            
        else:
            print("❌ Failed to read test file")
        
        print("✅ File processing test completed")
        
    except Exception as e:
        print(f"❌ File processing test failed: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Run all tests"""
    print("TSK/MAP File Processor Tool - Test Suite")
    print("=" * 50)
    
    # Run tests
    test_ab_map_tool()
    test_full_map_tool()
    test_memory_cleanup()
    test_file_processing()
    
    print("\n" + "=" * 50)
    print("Test suite completed!")
    print("\nTo run the main application:")
    print("cd ..")
    print("python main.py")

if __name__ == "__main__":
    main()
