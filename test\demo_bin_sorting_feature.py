#!/usr/bin/env python3
"""
Demo script for the new bin sorting feature in Full Map Tool
Shows the difference between sorting by quantity vs by bin number
"""

import os
import sys
import tempfile

# Add parent directory to path to import modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def demo_bin_sorting_comparison():
    """Demonstrate the difference between the two sorting methods"""
    print("🎯 Bin Sorting Feature Demo")
    print("=" * 60)
    
    try:
        from tsk_map_processor import TSKMapProcessor
        from full_map_processor import FullMapProcessor
        
        # Find a test MAP file
        test_files = []
        test_dir = os.path.dirname(__file__)
        parent_dir = os.path.dirname(test_dir)
        
        for root, dirs, files in os.walk(parent_dir):
            for file in files:
                if file.endswith('.map') or file.endswith('.MAP'):
                    test_files.append(os.path.join(root, file))
                    if len(test_files) >= 1:
                        break
            if test_files:
                break
        
        if not test_files:
            print("❌ No MAP files found for demo")
            return False
        
        test_file = test_files[0]
        print(f"📁 Demo file: {os.path.basename(test_file)}")
        
        # Process the file
        processor = TSKMapProcessor()
        processor.file_path = test_file
        if not processor.parse_file_header():
            print("❌ Failed to parse file")
            return False

        if not processor.process_die_data():
            print("❌ Failed to process die data")
            return False
        
        # Get file info
        info = processor.get_file_info()
        stats = processor.get_test_statistics()
        
        print(f"\n📊 File Information:")
        print(f"   Dimensions: {info['columnsize']} x {info['rowsize']}")
        print(f"   Total tested dies: {stats['total_tested']:,}")
        print(f"   Passed dies: {stats['passed_dies']:,}")
        print(f"   Overall yield: {stats['yield_percentage']:.2f}%")
        
        # Demo 1: Sort by quantity (default behavior)
        print(f"\n🔢 OPTION 1: Sort by Quantity (Descending)")
        print("   This is the DEFAULT behavior (checkbox checked)")
        print("   Bins are ordered by how many dies they contain")
        print("   " + "-" * 50)
        
        bin_stats_qty = processor.get_bin_statistics(sort_by_quantity=True)
        
        print(f"   {'Rank':<4} {'Bin Name':<10} {'Quantity':<10} {'Yield%':<8}")
        print("   " + "-" * 32)
        
        for i, bin_data in enumerate(bin_stats_qty[:8], 1):  # Show top 8
            bin_name = bin_data['bin_name']
            quantity = bin_data['quantity']
            yield_pct = bin_data['yield_percentage']
            print(f"   {i:<4} {bin_name:<10} {quantity:<10,} {yield_pct:<8.2f}")
        
        if len(bin_stats_qty) > 8:
            print(f"   ... and {len(bin_stats_qty) - 8} more bins")
        
        # Demo 2: Sort by bin number
        print(f"\n🔢 OPTION 2: Sort by Bin Number (Ascending)")
        print("   This is the NEW option (checkbox unchecked)")
        print("   Bins are ordered from bin0, bin1, bin2, ... onwards")
        print("   " + "-" * 50)
        
        bin_stats_num = processor.get_bin_statistics(sort_by_quantity=False)
        
        print(f"   {'Rank':<4} {'Bin Name':<10} {'Quantity':<10} {'Yield%':<8}")
        print("   " + "-" * 32)
        
        for i, bin_data in enumerate(bin_stats_num[:8], 1):  # Show first 8
            bin_name = bin_data['bin_name']
            quantity = bin_data['quantity']
            yield_pct = bin_data['yield_percentage']
            print(f"   {i:<4} {bin_name:<10} {quantity:<10,} {yield_pct:<8.2f}")
        
        if len(bin_stats_num) > 8:
            print(f"   ... and {len(bin_stats_num) - 8} more bins")
        
        # Show the key difference
        print(f"\n🔍 KEY DIFFERENCES:")
        print("   " + "=" * 50)
        
        # Find the most common bin
        most_common_bin = bin_stats_qty[0]
        most_common_name = most_common_bin['bin_name']
        most_common_qty = most_common_bin['quantity']
        
        # Find where this bin appears in the number-sorted list
        number_sorted_position = None
        for i, bin_data in enumerate(bin_stats_num, 1):
            if bin_data['bin_name'] == most_common_name:
                number_sorted_position = i
                break
        
        print(f"   Most common bin: {most_common_name} ({most_common_qty:,} dies)")
        print(f"   • In quantity sort: Position #1 (first)")
        print(f"   • In number sort: Position #{number_sorted_position}")
        
        # Find bin0 position in quantity sort
        bin0_position = None
        for i, bin_data in enumerate(bin_stats_qty, 1):
            if bin_data['bin_name'] == 'bin0':
                bin0_position = i
                break
        
        if bin0_position:
            print(f"\n   bin0 (first by number):")
            print(f"   • In number sort: Position #1 (first)")
            print(f"   • In quantity sort: Position #{bin0_position}")
        
        # Usage guidance
        print(f"\n💡 WHEN TO USE EACH OPTION:")
        print("   " + "=" * 50)
        print("   ✅ Sort by Quantity (DEFAULT):")
        print("      • When you want to see the most common failures first")
        print("      • For yield analysis and defect prioritization")
        print("      • Most useful for production analysis")
        print()
        print("   ✅ Sort by Bin Number:")
        print("      • When you want bins in test program order")
        print("      • For systematic review of all test conditions")
        print("      • When bin numbers have logical meaning (0,1,2...)")
        print("      • For consistency with test program flow")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during demo: {e}")
        import traceback
        traceback.print_exc()
        return False


def demo_full_map_processor():
    """Demo the FullMapProcessor with different sorting options"""
    print(f"\n" + "=" * 60)
    print("🎯 Full Map Processor Demo")
    print("=" * 60)
    
    try:
        from full_map_processor import FullMapProcessor
        
        # Create processor instances with different settings
        print("📋 Creating FullMapProcessor instances...")
        
        # Default processor (sort by quantity)
        processor_default = FullMapProcessor()
        print(f"   Default processor - sort_by_quantity: {processor_default.sort_by_quantity}")
        
        # Processor with number sorting
        processor_number = FullMapProcessor()
        processor_number.set_sort_by_quantity(False)
        print(f"   Number sort processor - sort_by_quantity: {processor_number.sort_by_quantity}")
        
        print("\n✅ Both processor configurations are ready!")
        print("   These settings will be applied when processing MAP files")
        print("   and generating Excel output with bin statistics.")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during FullMapProcessor demo: {e}")
        return False


def demo_gui_integration():
    """Demo how the feature integrates with the GUI"""
    print(f"\n" + "=" * 60)
    print("🎯 GUI Integration Demo")
    print("=" * 60)
    
    try:
        import tkinter as tk
        from full_map_tool_frame import FullMapToolFrame
        
        print("🖥️  GUI Integration Points:")
        print("   " + "=" * 40)
        
        # Create a mock root and controller for testing
        root = tk.Tk()
        root.withdraw()  # Hide the window
        
        class MockController:
            def __init__(self):
                self.root = root
        
        controller = MockController()
        
        # Create the frame
        frame = FullMapToolFrame(root, controller)
        
        # Check the new variable
        print(f"   ✅ New GUI variable added: sort_by_quantity")
        print(f"   ✅ Default value: {frame.sort_by_quantity.get()}")
        
        # Test changing the value
        frame.sort_by_quantity.set(False)
        print(f"   ✅ Can be changed to: {frame.sort_by_quantity.get()}")
        
        frame.sort_by_quantity.set(True)
        print(f"   ✅ Can be changed back to: {frame.sort_by_quantity.get()}")
        
        print("\n📋 GUI Components:")
        print("   ✅ Checkbox added to 'Output Options' section")
        print("   ✅ Text: 'Sort bins by quantity (descending order)'")
        print("   ✅ Default state: Checked (True)")
        print("   ✅ User can uncheck to sort by bin number instead")
        
        print("\n🔄 Processing Flow:")
        print("   1. User selects MAP files")
        print("   2. User chooses sorting option (checkbox)")
        print("   3. User clicks 'Process All Files'")
        print("   4. Setting is passed to FullMapProcessor")
        print("   5. Excel output uses the selected sorting method")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ Error during GUI demo: {e}")
        return False


def main():
    """Run the complete demo"""
    print("🎉 BIN SORTING FEATURE DEMO")
    print("=" * 80)
    print("This demo shows the new bin sorting feature added to Full Map Tool")
    print("=" * 80)
    
    # Run demos
    demo1 = demo_bin_sorting_comparison()
    demo2 = demo_full_map_processor()
    demo3 = demo_gui_integration()
    
    # Summary
    print("\n" + "=" * 80)
    print("📋 DEMO SUMMARY")
    print("=" * 80)
    
    if demo1 and demo2 and demo3:
        print("🎉 ALL DEMOS COMPLETED SUCCESSFULLY!")
        print("\n🎯 NEW FEATURE SUMMARY:")
        print("   ✅ Added checkbox: 'Sort bins by quantity (descending order)'")
        print("   ✅ Default: Checked (maintains existing behavior)")
        print("   ✅ Unchecked: Sort by bin number (0, 1, 2, ... ascending)")
        print("   ✅ Affects A14~AXX, B14~BXX, C14~CXX columns in Excel")
        print("   ✅ Maintains all existing functionality")
        print("   ✅ GUI space optimized (minimal additional space)")
        
        print("\n👥 USER BENEFITS:")
        print("   • Flexibility in data presentation")
        print("   • Better alignment with test program flow")
        print("   • Maintains backward compatibility")
        print("   • Easy to understand and use")
        
    else:
        print("❌ Some demos failed. Please check the implementation.")
    
    return demo1 and demo2 and demo3


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
