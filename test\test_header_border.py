#!/usr/bin/env python3
"""
Test script for the header border functionality in Bin_Summary sheet
Tests the thick black border around the header area (A1:L2)
"""

import os
import sys
import tempfile

# Add parent directory to path to import modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_header_border_functionality():
    """Test the thick black border around header area"""
    print("🖼️  Testing Header Border Functionality")
    print("=" * 50)
    
    try:
        from full_map_processor import FullMapProcessor
        from config_reader import ConfigReader
        from openpyxl import Workbook
        
        # Create test setup
        processor = FullMapProcessor()
        
        # Create mock config
        config_reader = ConfigReader()
        config_reader.device_name = "TestDevice_BorderDemo"
        config_reader.vendor_name = "BorderTech_Corp"
        processor.set_config_reader(config_reader)
        
        # Create test workbook
        workbook = Workbook()
        worksheet = workbook.active
        worksheet.title = "Bin_Summary"
        
        # Create mock processors data
        mock_processors = {
            "BORDER_TEST-W01.map": {
                "processor": MockTSKProcessor(2000, 1900),  # 95% yield
                "file_path": "BORDER_TEST-W01.map"
            },
            "BORDER_TEST-W02.map": {
                "processor": MockTSKProcessor(2100, 1890),  # 90% yield
                "file_path": "BORDER_TEST-W02.map"
            }
        }
        
        print("1️⃣ Creating header with border...")
        processor._create_summary_header(worksheet, mock_processors)
        
        # Verify border properties
        print("2️⃣ Verifying border properties...")
        
        # Check corner cells
        corner_cells = {
            'A1': 'Top-left corner',
            'L1': 'Top-right corner', 
            'A2': 'Bottom-left corner',
            'L2': 'Bottom-right corner'
        }
        
        for cell_ref, description in corner_cells.items():
            cell = worksheet[cell_ref]
            border = cell.border
            
            print(f"   {description} ({cell_ref}):")
            print(f"     Top: {border.top.style if border.top else 'None'}")
            print(f"     Bottom: {border.bottom.style if border.bottom else 'None'}")
            print(f"     Left: {border.left.style if border.left else 'None'}")
            print(f"     Right: {border.right.style if border.right else 'None'}")
        
        # Check edge cells
        edge_cells = ['B1', 'F1', 'J1', 'B2', 'F2', 'J2']
        print("   Edge cells border check:")
        for cell_ref in edge_cells:
            cell = worksheet[cell_ref]
            border = cell.border
            has_thick_border = (
                (border.top and border.top.style == 'thick') or
                (border.bottom and border.bottom.style == 'thick') or
                (border.left and border.left.style == 'thick') or
                (border.right and border.right.style == 'thick')
            )
            print(f"     {cell_ref}: {'✅' if has_thick_border else '❌'} Has thick border")
        
        # Verify border color
        print("3️⃣ Verifying border color...")
        test_cell = worksheet['A1']
        if test_cell.border.left and test_cell.border.left.color:
            color = test_cell.border.left.color.rgb
            if color == '00000000' or color == 'FF000000':  # Black color variations
                print("   ✅ Border color is black")
            else:
                print(f"   ❌ Border color is {color}, expected black")
                return False
        
        workbook.close()
        return True
        
    except Exception as e:
        print(f"❌ Error during border testing: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_border_visual_output():
    """Create a visual sample to demonstrate the border effect"""
    print("\n🖼️  Creating Visual Border Demo")
    print("=" * 50)
    
    try:
        from full_map_processor import FullMapProcessor
        from config_reader import ConfigReader
        from openpyxl import Workbook
        
        # Create complete example with border
        processor = FullMapProcessor()
        
        # Setup config
        config_reader = ConfigReader()
        config_reader.device_name = "BorderDemo_Chip_V1.0"
        config_reader.vendor_name = "VisualTech_Solutions"
        processor.set_config_reader(config_reader)
        
        # Create workbook
        workbook = Workbook()
        worksheet = workbook.active
        worksheet.title = "Bin_Summary"
        
        # Mock data for demonstration
        mock_processors = {
            "DEMO2024_BORDER-W01.map": {"processor": MockTSKProcessor(3000, 2850), "file_path": "DEMO2024_BORDER-W01.map"},
            "DEMO2024_BORDER-W02.map": {"processor": MockTSKProcessor(3100, 2945), "file_path": "DEMO2024_BORDER-W02.map"},
            "DEMO2024_BORDER-W03.map": {"processor": MockTSKProcessor(2950, 2803), "file_path": "DEMO2024_BORDER-W03.map"},
        }
        
        # Create the complete formatted sheet with border
        print("1️⃣ Creating complete Bin_Summary with border...")
        processor._create_bin_summary_sheet(worksheet, mock_processors, workbook)
        
        # Save sample file
        sample_file = os.path.join(tempfile.gettempdir(), "Bin_Summary_With_Border_Demo.xlsx")
        workbook.save(sample_file)
        workbook.close()
        
        print(f"✅ Border demo file created: {sample_file}")
        print("   Features demonstrated:")
        print("   • Thick black border around header area (A1:L2)")
        print("   • Professional formatting maintained")
        print("   • Clear visual separation of header information")
        print("   • Enhanced professional appearance")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating border demo: {e}")
        return False


def test_border_integration():
    """Test that border doesn't interfere with existing formatting"""
    print("\n🖼️  Testing Border Integration")
    print("=" * 50)
    
    try:
        from full_map_processor import FullMapProcessor
        from openpyxl import Workbook
        
        # Create processor
        processor = FullMapProcessor()
        processor._setup_summary_styles()
        
        # Create test workbook
        workbook = Workbook()
        worksheet = workbook.active
        
        # Test that border method exists
        print("1️⃣ Testing border method availability...")
        if hasattr(processor, '_add_header_border'):
            print("   ✅ _add_header_border method available")
        else:
            print("   ❌ _add_header_border method missing")
            return False
        
        # Test border application
        print("2️⃣ Testing border application...")
        try:
            processor._add_header_border(worksheet)
            print("   ✅ Border applied successfully")
        except Exception as e:
            print(f"   ❌ Border application failed: {e}")
            return False
        
        # Test that existing cell formatting is preserved
        print("3️⃣ Testing formatting preservation...")
        
        # Add some content and formatting first
        worksheet['A1'].value = "Test Header"
        worksheet['A1'].font = processor.fonts['header_large']
        worksheet['A1'].fill = processor.fills['header_primary']
        
        # Apply border
        processor._add_header_border(worksheet)
        
        # Check that original formatting is preserved
        cell_a1 = worksheet['A1']
        if (cell_a1.font.name == 'Segoe UI' and 
            cell_a1.font.bold and 
            cell_a1.border.left.style == 'thick'):
            print("   ✅ Original formatting preserved with border added")
        else:
            print("   ❌ Original formatting not preserved")
            return False
        
        workbook.close()
        return True
        
    except Exception as e:
        print(f"❌ Error during integration testing: {e}")
        return False


class MockTSKProcessor:
    """Mock TSKMapProcessor for testing"""
    def __init__(self, total_tested, pass_count):
        self.total_tested = total_tested
        self.pass_count = pass_count
    
    def get_test_statistics(self):
        return {
            'total_tested': self.total_tested,
            'pass_count': self.pass_count,
            'yield_percentage': (self.pass_count / self.total_tested * 100) if self.total_tested > 0 else 0.0
        }


def main():
    """Run all border tests"""
    print("🖼️  HEADER BORDER TEST SUITE")
    print("=" * 70)
    
    # Run tests
    test1 = test_header_border_functionality()
    test2 = test_border_visual_output()
    test3 = test_border_integration()
    
    # Summary
    print("\n" + "=" * 70)
    print("📋 BORDER TEST RESULTS")
    print("=" * 70)
    print(f"   Border Functionality: {'PASS' if test1 else 'FAIL'}")
    print(f"   Visual Output: {'PASS' if test2 else 'FAIL'}")
    print(f"   Integration: {'PASS' if test3 else 'FAIL'}")
    
    all_passed = test1 and test2 and test3
    print(f"\nOVERALL: {'ALL TESTS PASSED' if all_passed else 'SOME TESTS FAILED'}")
    
    if all_passed:
        print("\n🎉 SUCCESS! Header border functionality is working perfectly!")
        print("\n🖼️  BORDER FEATURES:")
        print("   ✅ Thick black border around header area (A1:L2)")
        print("   ✅ Professional visual separation")
        print("   ✅ Preserves existing cell formatting")
        print("   ✅ Clean corner and edge handling")
        print("   ✅ Enhanced professional appearance")
        print("   ✅ Matches the design specification exactly")
    else:
        print("\n❌ Some border tests failed. Please check the implementation.")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
