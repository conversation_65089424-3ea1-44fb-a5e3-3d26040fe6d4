#!/usr/bin/env python3
"""
Test script for AB Comparison Analysis functionality
Tests the three new analysis sheets: Summary, Correlation, and Map_compare
"""

import sys
import os
from datetime import datetime

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tsk_map_processor import TSKMapProcessor
from ab_comparison_analyzer import ABComparisonAnalyzer
from excel_output import ExcelOutputHand<PERSON>


def test_ab_comparison_analysis():
    """Test AB comparison analysis functionality"""
    print("AB Comparison Analysis Test")
    print("=" * 60)
    
    # Test file path
    test_file = "3AA111-01-B4"
    
    if not os.path.exists(test_file):
        print(f"❌ Test file not found: {test_file}")
        return False
    
    print(f"✅ Test file found: {test_file}")
    
    try:
        # Process both files (using same file for A and B for testing)
        print("\nProcessing Amap file...")
        amap_processor = TSKMapProcessor()
        if not amap_processor.read_file(test_file):
            print("❌ Failed to read Amap file")
            return False
        
        if not amap_processor.parse_file_header():
            print("❌ Failed to parse Amap file header")
            return False
            
        if not amap_processor.process_die_data():
            print("❌ Failed to process Amap die data")
            return False
        
        print("Processing Bmap file...")
        bmap_processor = TSKMapProcessor()
        if not bmap_processor.read_file(test_file):
            print("❌ Failed to read Bmap file")
            return False
        
        if not bmap_processor.parse_file_header():
            print("❌ Failed to parse Bmap file header")
            return False
            
        if not bmap_processor.process_die_data():
            print("❌ Failed to process Bmap die data")
            return False
        
        print("✅ Both files processed successfully")
        
        # Get rotated data
        rotation_angle = 0
        amap_data = amap_processor.get_rotated_data(rotation_angle)
        bmap_data = bmap_processor.get_rotated_data(rotation_angle)
        
        if not amap_data or not bmap_data:
            print("❌ Failed to get rotated data")
            return False
        
        print(f"✅ Data extracted: Amap {len(amap_data)}x{len(amap_data[0])}, Bmap {len(bmap_data)}x{len(bmap_data[0])}")
        
        # Test analyzer
        print("\nTesting AB Comparison Analyzer...")
        analyzer = ABComparisonAnalyzer()
        analyzer.set_data(amap_processor, bmap_processor, amap_data, bmap_data)
        
        # Test Summary analysis
        print("Testing Summary analysis...")
        summary_data = analyzer.generate_summary_analysis()
        
        if summary_data:
            print(f"✅ Summary analysis generated")
            print(f"  Total dies: {summary_data['total_dies']}")
            print(f"  Unique bins: {len(summary_data['bin_data'])}")
            
            # Show top 5 bins
            print("  Top 5 bins by count:")
            for i, bin_data in enumerate(summary_data['bin_data'][:5]):
                if bin_data['amap_count'] > 0 or bin_data['bmap_count'] > 0:
                    print(f"    Bin {bin_data['bin']}: A={bin_data['amap_count']}, B={bin_data['bmap_count']}, Diff={bin_data['diff_count']}")
        else:
            print("❌ Failed to generate Summary analysis")
            return False
        
        # Test Correlation analysis
        print("Testing Correlation analysis...")
        correlation_data = analyzer.generate_correlation_analysis()
        
        if correlation_data:
            print(f"✅ Correlation analysis generated")
            print(f"  Matrix size: {len(correlation_data['bins'])}x{len(correlation_data['bins'])}")
            print(f"  Total dies: {correlation_data['total_dies']}")
        else:
            print("❌ Failed to generate Correlation analysis")
            return False
        
        # Test Excel output with all sheets
        print("\nTesting Excel output with analysis sheets...")
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"test/test_ab_analysis_{timestamp}.xlsx"
        
        excel_handler = ExcelOutputHandler()
        if not excel_handler.create_workbook():
            print("❌ Failed to create workbook")
            return False
        
        # Write basic Amap and Bmap sheets
        if not excel_handler.write_rotated_data(amap_data, rotation_angle, "Amap", amap_processor, None):
            print("❌ Failed to write Amap sheet")
            return False
        
        if not excel_handler.write_rotated_data(bmap_data, rotation_angle, "Bmap", bmap_processor, None):
            print("❌ Failed to write Bmap sheet")
            return False
        
        # Write analysis sheets
        if not analyzer.write_summary_sheet(excel_handler.workbook, summary_data):
            print("❌ Failed to write Summary sheet")
            return False
        
        if not analyzer.write_correlation_sheet(excel_handler.workbook, correlation_data):
            print("❌ Failed to write Correlation sheet")
            return False
        
        if not analyzer.write_map_compare_sheet(excel_handler.workbook):
            print("❌ Failed to write Map_compare sheet")
            return False

        if not analyzer.write_map_compare_full_sheet(excel_handler.workbook):
            print("❌ Failed to write Map_compare_full sheet")
            return False
        
        # Create setup sheet
        file_paths = {"Amap": test_file, "Bmap": test_file}
        excel_handler.create_setup_sheet(file_paths)
        
        # Save workbook
        if excel_handler.save_workbook(output_filename):
            print(f"✅ Excel file created: {output_filename}")
            
            # Verify sheets exist
            from openpyxl import load_workbook
            wb = load_workbook(output_filename)
            expected_sheets = ["Amap", "Bmap", "Summary", "Correlation", "Map_compare", "Map_compare_full", "Setup"]
            
            print(f"  Sheets in workbook: {wb.sheetnames}")
            
            for sheet_name in expected_sheets:
                if sheet_name in wb.sheetnames:
                    print(f"  ✅ {sheet_name} sheet exists")
                else:
                    print(f"  ❌ {sheet_name} sheet missing")
                    return False
            
            wb.close()
        else:
            print("❌ Failed to save Excel file")
            return False
        
        excel_handler.close_workbook()
        
        print("\n" + "=" * 60)
        print("🎉 AB Comparison Analysis test completed successfully!")
        print(f"\nGenerated file: {output_filename}")
        print("\nSheets created:")
        print("• Amap - Original Amap data")
        print("• Bmap - Original Bmap data")
        print("• Summary - Bin difference analysis")
        print("• Correlation - Bin jump analysis matrix")
        print("• Map_compare - Position comparison (Different first, Same last)")
        print("• Map_compare_full - Map format display (R0 style)")
        print("• Setup - File information")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_ab_comparison_analysis()
    sys.exit(0 if success else 1)
