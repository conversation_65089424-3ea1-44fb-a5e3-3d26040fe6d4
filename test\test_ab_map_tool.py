#!/usr/bin/env python3
"""
Test script for AB MAP Tool functionality
Tests the three processing modes:
1. Amap mode - Single A file
2. Bmap mode - Single B file  
3. AB Compare mode - Dual file comparison
"""

import sys
import os
from datetime import datetime

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tsk_map_processor import TSKMapProcessor
from excel_output import ExcelOutputHandler


def test_ab_map_functionality():
    """Test AB Map tool functionality programmatically"""
    print("AB MAP Tool Functionality Test")
    print("=" * 50)
    
    # Test file path
    test_file = "3AA111-01-B4"
    
    if not os.path.exists(test_file):
        print(f"❌ Test file not found: {test_file}")
        return False
    
    print(f"✅ Test file found: {test_file}")
    
    try:
        # Test 1: Amap mode (single file processing)
        print("\nTest 1: Amap Mode")
        print("-" * 30)
        
        amap_processor = TSKMapProcessor()
        if not amap_processor.read_file(test_file):
            print("❌ Failed to read file for Amap test")
            return False
        
        if not amap_processor.parse_file_header():
            print("❌ Failed to parse file header for Amap test")
            return False
            
        if not amap_processor.process_die_data():
            print("❌ Failed to process die data for Amap test")
            return False
        
        # Create Amap Excel output
        amap_filename = f"test/test_amap_R0.xlsx"
        if create_single_excel(amap_processor, "Amap", 0, amap_filename, test_file):
            print(f"✅ Amap Excel created: {amap_filename}")
        else:
            print("❌ Failed to create Amap Excel")
            return False
        
        # Test 2: Bmap mode (single file processing)
        print("\nTest 2: Bmap Mode")
        print("-" * 30)
        
        bmap_processor = TSKMapProcessor()
        if not bmap_processor.read_file(test_file):
            print("❌ Failed to read file for Bmap test")
            return False
        
        if not bmap_processor.parse_file_header():
            print("❌ Failed to parse file header for Bmap test")
            return False
            
        if not bmap_processor.process_die_data():
            print("❌ Failed to process die data for Bmap test")
            return False
        
        # Create Bmap Excel output
        bmap_filename = f"test/test_bmap_R0.xlsx"
        if create_single_excel(bmap_processor, "Bmap", 0, bmap_filename, test_file):
            print(f"✅ Bmap Excel created: {bmap_filename}")
        else:
            print("❌ Failed to create Bmap Excel")
            return False
        
        # Test 3: AB Compare mode (dual file processing)
        print("\nTest 3: AB Compare Mode")
        print("-" * 30)
        
        # Use the same file for both A and B for testing purposes
        amap_processor_compare = TSKMapProcessor()
        bmap_processor_compare = TSKMapProcessor()
        
        # Process both files
        for processor, name in [(amap_processor_compare, "Amap"), (bmap_processor_compare, "Bmap")]:
            if not processor.read_file(test_file):
                print(f"❌ Failed to read file for {name} comparison test")
                return False
            
            if not processor.parse_file_header():
                print(f"❌ Failed to parse file header for {name} comparison test")
                return False
                
            if not processor.process_die_data():
                print(f"❌ Failed to process die data for {name} comparison test")
                return False
        
        # Create AB comparison Excel output
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        compare_filename = f"test/test_AB_map_compare_{timestamp}_R0.xlsx"
        
        if create_comparison_excel(amap_processor_compare, bmap_processor_compare, 
                                 test_file, test_file, 0, compare_filename):
            print(f"✅ AB Comparison Excel created: {compare_filename}")
        else:
            print("❌ Failed to create AB Comparison Excel")
            return False
        
        print("\n" + "=" * 50)
        print("🎉 All AB MAP Tool tests passed!")
        print("\nGenerated files:")
        print(f"• {amap_filename} (Amap mode)")
        print(f"• {bmap_filename} (Bmap mode)")
        print(f"• {compare_filename} (AB Compare mode)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False


def create_single_excel(processor, sheet_name, rotation_angle, output_filename, file_path):
    """Create Excel output for single file mode"""
    try:
        excel_handler = ExcelOutputHandler()
        
        if not excel_handler.create_workbook():
            return False
        
        # Get rotated data
        rotated_data = processor.get_rotated_data(rotation_angle)
        if not rotated_data:
            return False
        
        # Write data to worksheet
        if not excel_handler.write_rotated_data(rotated_data, rotation_angle, sheet_name, processor, None):
            return False
        
        # Create setup sheet
        file_paths = {sheet_name: file_path}
        excel_handler.create_setup_sheet(file_paths)
        
        # Save workbook
        success = excel_handler.save_workbook(output_filename)
        excel_handler.close_workbook()
        
        return success
        
    except Exception as e:
        print(f"Error creating single Excel output: {e}")
        return False


def create_comparison_excel(amap_processor, bmap_processor, amap_path, bmap_path, rotation_angle, output_filename):
    """Create Excel output for AB comparison mode with advanced analysis"""
    try:
        from ab_comparison_analyzer import ABComparisonAnalyzer

        excel_handler = ExcelOutputHandler()

        if not excel_handler.create_workbook():
            return False

        # Process Amap data
        amap_data = amap_processor.get_rotated_data(rotation_angle)
        if not amap_data:
            return False

        # Write Amap data to first sheet
        if not excel_handler.write_rotated_data(amap_data, rotation_angle, "Amap", amap_processor, None):
            return False

        # Process Bmap data
        bmap_data = bmap_processor.get_rotated_data(rotation_angle)
        if not bmap_data:
            return False

        # Write Bmap data to second sheet
        if not excel_handler.write_rotated_data(bmap_data, rotation_angle, "Bmap", bmap_processor, None):
            return False

        # Create advanced comparison analysis
        analyzer = ABComparisonAnalyzer()
        analyzer.set_data(amap_processor, bmap_processor, amap_data, bmap_data)

        # Generate Summary sheet
        summary_data = analyzer.generate_summary_analysis()
        if summary_data:
            analyzer.write_summary_sheet(excel_handler.workbook, summary_data)

        # Generate Correlation sheet
        correlation_data = analyzer.generate_correlation_analysis()
        if correlation_data:
            analyzer.write_correlation_sheet(excel_handler.workbook, correlation_data)

        # Generate Map_compare sheet
        analyzer.write_map_compare_sheet(excel_handler.workbook)

        # Generate Map_compare_full sheet
        analyzer.write_map_compare_full_sheet(excel_handler.workbook)

        # Create setup sheet with both file paths
        file_paths = {
            "Amap": amap_path,
            "Bmap": bmap_path
        }
        excel_handler.create_setup_sheet(file_paths)

        # Save workbook
        success = excel_handler.save_workbook(output_filename)
        excel_handler.close_workbook()

        return success

    except Exception as e:
        print(f"Error creating comparison Excel output: {e}")
        return False


if __name__ == "__main__":
    success = test_ab_map_functionality()
    sys.exit(0 if success else 1)
