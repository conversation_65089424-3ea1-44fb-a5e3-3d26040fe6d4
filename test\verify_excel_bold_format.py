#!/usr/bin/env python3
"""
Verify Excel Bold Format - Check that bin names in generated Excel files are bold
"""

import sys
import os

try:
    from openpyxl import load_workbook
except ImportError:
    print("Error: openpyxl not installed. Please run: pip install openpyxl")
    exit(1)


def verify_excel_bold_formatting(excel_file):
    """Verify that bin names in Excel file are bold"""
    print(f"Verifying Excel Bold Formatting: {os.path.basename(excel_file)}")
    print("=" * 60)
    
    if not os.path.exists(excel_file):
        print(f"❌ Excel file not found: {excel_file}")
        return False
    
    try:
        # Load Excel workbook
        workbook = load_workbook(excel_file)
        worksheet = workbook.active
        
        print(f"✅ Excel file loaded successfully")
        print(f"  Worksheet: {worksheet.title}")
        print(f"  Max row: {worksheet.max_row}")
        print(f"  Max column: {worksheet.max_column}")
        
        # Check header formatting (A13)
        header_cell = worksheet['A13']
        header_value = header_cell.value
        header_bold = header_cell.font.bold if header_cell.font else False
        
        print(f"\nHeader Check (A13):")
        print(f"  Value: '{header_value}'")
        print(f"  Bold: {header_bold}")
        
        if header_bold:
            print(f"  ✅ Header is bold (as expected)")
        else:
            print(f"  ⚠️  Header is not bold")
        
        # Check bin name formatting (A14 onwards)
        print(f"\nBin Name Formatting Check (A14+):")
        print(f"{'Row':<4} {'Value':<30} {'Bold':<6} {'Status':<8}")
        print("-" * 48)
        
        bold_count = 0
        total_bin_count = 0
        
        for row in range(14, min(worksheet.max_row + 1, 25)):  # Check up to row 25
            cell = worksheet.cell(row=row, column=1)  # Column A
            value = cell.value
            
            if value is not None and str(value).strip():
                total_bin_count += 1
                is_bold = cell.font.bold if cell.font else False
                
                if is_bold:
                    bold_count += 1
                    status = "✓"
                else:
                    status = "✗"
                
                value_str = str(value)[:29]  # Truncate long values
                print(f"A{row:<3} {value_str:<30} {str(is_bold):<6} {status:<8}")
        
        print("-" * 48)
        print(f"Summary: {bold_count}/{total_bin_count} bin names are bold")
        
        if bold_count == total_bin_count and total_bin_count > 0:
            print(f"✅ All bin names are bold!")
            success = True
        elif bold_count > 0:
            print(f"⚠️  Some bin names are bold ({bold_count}/{total_bin_count})")
            success = True
        else:
            print(f"❌ No bin names are bold")
            success = False
        
        # Check other columns are not bold (B and C should be normal)
        print(f"\nOther Columns Check (should not be bold):")
        
        for col, col_name in [(2, 'B'), (3, 'C')]:
            sample_cell = worksheet.cell(row=14, column=col)
            sample_value = sample_cell.value
            sample_bold = sample_cell.font.bold if sample_cell.font else False
            
            print(f"  {col_name}14: '{sample_value}' - Bold: {sample_bold}")
            
            if not sample_bold:
                print(f"    ✅ Column {col_name} is not bold (correct)")
            else:
                print(f"    ⚠️  Column {col_name} is bold (unexpected)")
        
        workbook.close()
        return success
        
    except Exception as e:
        print(f"❌ Error verifying Excel formatting: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main verification function"""
    print("Excel Bold Format Verification")
    print("Checking generated Excel files for bold bin names")
    print("=" * 70)
    
    # Get current directory
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Test files to verify
    test_files = [
        "test_bin_bold_with_config.xlsx",
        "test_bin_bold_no_config.xlsx",
        "test_bin_statistics_output.xlsx"
    ]
    
    success_count = 0
    total_count = 0
    
    for test_file in test_files:
        file_path = os.path.join(current_dir, test_file)
        
        if os.path.exists(file_path):
            total_count += 1
            if verify_excel_bold_formatting(file_path):
                success_count += 1
            print()  # Add spacing between files
        else:
            print(f"⚠️  Test file not found: {test_file}")
            print()
    
    print("=" * 70)
    print(f"Verification Summary: {success_count}/{total_count} files passed")
    
    if success_count == total_count and total_count > 0:
        print("🎉 All Excel files have correct bold formatting!")
        print("\nFormatting Verification Results:")
        print("✅ A13: Category header is bold")
        print("✅ A14~AXX: All bin names are bold")
        print("✅ B14~BXX: Quantities are normal font")
        print("✅ C14~CXX: Yield percentages are normal font")
        print("✅ Consistent formatting across all files")
    elif success_count > 0:
        print(f"⚠️  {success_count} out of {total_count} files have correct formatting")
    else:
        print("❌ No files have correct bold formatting")
    
    print(f"\nTest Files Location: test/ folder")
    print(f"• All generated Excel files are in the test directory")
    print(f"• Main project folder remains clean")


if __name__ == "__main__":
    main()
