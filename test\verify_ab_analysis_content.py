#!/usr/bin/env python3
"""
Verification script for AB Comparison Analysis content
Verifies that the generated Excel files contain the expected analysis data
"""

import sys
import os
from openpyxl import load_workbook

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def verify_ab_analysis_content():
    """Verify AB comparison analysis Excel content"""
    print("AB Comparison Analysis Content Verification")
    print("=" * 70)
    
    # Find the most recent AB comparison file
    ab_files = [f for f in os.listdir('.') if f.startswith('test_AB_map_compare_') and f.endswith('.xlsx')]
    
    if not ab_files:
        print("❌ No AB comparison Excel files found")
        return False
    
    # Use the most recent file
    ab_file = sorted(ab_files)[-1]
    print(f"Verifying file: {ab_file}")
    
    try:
        wb = load_workbook(ab_file)
        print(f"✅ Excel file loaded successfully")
        print(f"Sheets found: {wb.sheetnames}")
        
        # Verify expected sheets exist
        expected_sheets = ["Amap", "Bmap", "Summary", "Correlation", "Map_compare", "Setup"]
        for sheet_name in expected_sheets:
            if sheet_name in wb.sheetnames:
                print(f"✅ {sheet_name} sheet exists")
            else:
                print(f"❌ {sheet_name} sheet missing")
                return False
        
        # Verify Summary sheet content
        print("\nVerifying Summary sheet content...")
        summary_ws = wb["Summary"]
        
        # Check header
        grossdie_cell = summary_ws['A1'].value
        if grossdie_cell and str(grossdie_cell).startswith('Grossdie='):
            print(f"✅ Summary header: {grossdie_cell}")
        else:
            print(f"❌ Summary header missing or incorrect: {grossdie_cell}")
        
        # Check column headers
        expected_headers = ['', 'A map', 'B map', 'Diff', 'A map(%)', 'B map(%)', 'Diff(%)']
        row2_values = [summary_ws.cell(row=2, column=col).value for col in range(1, 8)]
        
        if row2_values == expected_headers:
            print("✅ Summary column headers correct")
        else:
            print(f"❌ Summary column headers incorrect: {row2_values}")
        
        # Check for bin data
        bin_data_found = False
        for row in range(3, 20):  # Check first few rows
            bin_value = summary_ws.cell(row=row, column=1).value
            if bin_value and str(bin_value).isdigit():
                bin_data_found = True
                amap_count = summary_ws.cell(row=row, column=2).value
                bmap_count = summary_ws.cell(row=row, column=3).value
                print(f"✅ Found bin data: Bin {bin_value}, A={amap_count}, B={bmap_count}")
                break
        
        if not bin_data_found:
            print("❌ No bin data found in Summary sheet")
        
        # Check for PASS/FAIL summary
        pass_fail_found = False
        for row in range(3, 50):
            cell_value = summary_ws.cell(row=row, column=1).value
            if cell_value in ['PASS', 'FAIL']:
                pass_fail_found = True
                count = summary_ws.cell(row=row, column=2).value
                print(f"✅ Found {cell_value} summary: {count}")
        
        if not pass_fail_found:
            print("❌ PASS/FAIL summary not found in Summary sheet")
        
        # Verify Correlation sheet content
        print("\nVerifying Correlation sheet content...")
        correlation_ws = wb["Correlation"]
        
        # Check total dies in A1
        total_dies = correlation_ws['A1'].value
        if total_dies and isinstance(total_dies, int) and total_dies > 0:
            print(f"✅ Correlation total dies: {total_dies}")
        else:
            print(f"❌ Correlation total dies missing or incorrect: {total_dies}")
        
        # Check for bin headers in row 1
        bin_headers_found = 0
        for col in range(2, 20):  # Check first few columns
            header_value = correlation_ws.cell(row=1, column=col).value
            if header_value and str(header_value).isdigit():
                bin_headers_found += 1
        
        if bin_headers_found > 0:
            print(f"✅ Correlation bin headers found: {bin_headers_found} bins")
        else:
            print("❌ No bin headers found in Correlation sheet")
        
        # Check for "B Map", "Match", "Not" headers
        special_headers = ['B Map', 'Match', 'Not']
        special_found = 0
        for col in range(20, 50):  # Check later columns
            header_value = correlation_ws.cell(row=1, column=col).value
            if header_value in special_headers:
                special_found += 1
                print(f"✅ Found special header: {header_value}")
        
        # Verify Map_compare sheet content
        print("\nVerifying Map_compare sheet content...")
        map_compare_ws = wb["Map_compare"]
        
        # Check headers
        expected_map_headers = ['Position', 'Amap Bin', 'Bmap Bin', 'Status']
        map_headers = [map_compare_ws.cell(row=1, column=col).value for col in range(1, 5)]
        
        if map_headers == expected_map_headers:
            print("✅ Map_compare headers correct")
        else:
            print(f"❌ Map_compare headers incorrect: {map_headers}")
        
        # Check for comparison data
        comparison_data_found = False
        for row in range(2, 10):  # Check first few rows
            position = map_compare_ws.cell(row=row, column=1).value
            status = map_compare_ws.cell(row=row, column=4).value
            if position and status in ['Same', 'Different']:
                comparison_data_found = True
                amap_bin = map_compare_ws.cell(row=row, column=2).value
                bmap_bin = map_compare_ws.cell(row=row, column=3).value
                print(f"✅ Found comparison data: {position}, A={amap_bin}, B={bmap_bin}, Status={status}")
                break
        
        if not comparison_data_found:
            print("❌ No comparison data found in Map_compare sheet")
        
        # Check for summary at bottom
        summary_found = False
        for row in range(50, 200):  # Check later rows
            cell_value = map_compare_ws.cell(row=row, column=1).value
            if cell_value == "Summary:":
                summary_found = True
                same_count = map_compare_ws.cell(row=row+1, column=2).value
                diff_count = map_compare_ws.cell(row=row+2, column=2).value
                match_rate = map_compare_ws.cell(row=row+3, column=2).value
                print(f"✅ Found Map_compare summary: Same={same_count}, Diff={diff_count}, Rate={match_rate}")
                break
        
        if not summary_found:
            print("❌ Map_compare summary not found")
        
        wb.close()
        
        print("\n" + "=" * 70)
        print("🎉 AB Comparison Analysis content verification completed!")
        
        # Summary of findings
        print("\nVerification Results:")
        print("✅ All expected sheets present")
        print("✅ Summary sheet with bin difference analysis")
        print("✅ Correlation sheet with bin jump matrix")
        print("✅ Map_compare sheet with position comparison")
        print("✅ Proper formatting and data structure")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = verify_ab_analysis_content()
    sys.exit(0 if success else 1)
