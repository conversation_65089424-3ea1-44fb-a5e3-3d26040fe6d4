#!/usr/bin/env python3
"""
Tool Selector Frame - Choose between AB Map Tool and Full Map Tool
Frame-based version for main application controller
Author: Yuribytes
Company: Chipone TE development Team
"""

import tkinter as tk
from tkinter import ttk
import os


class ToolSelectorFrame:
    """Tool selector as a frame for the main application"""
    
    def __init__(self, parent, app_controller):
        self.parent = parent
        self.app_controller = app_controller
        self.main_frame = None
        
        self.create_widgets()
    
    def create_widgets(self):
        """Create the tool selector interface"""
        # Main frame
        self.main_frame = ttk.Frame(self.parent, padding="20")
        
        # Title
        title_label = ttk.Label(self.main_frame, 
                               text="TSK/MAP File Processor Tool", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 10))
        
        # Subtitle
        subtitle_label = ttk.Label(self.main_frame, 
                                  text="Select a tool to begin processing", 
                                  font=("Arial", 11))
        subtitle_label.grid(row=1, column=0, columnspan=2, pady=(0, 30))
        
        # Tool selection frame
        tools_frame = ttk.Frame(self.main_frame)
        tools_frame.grid(row=2, column=0, columnspan=2, pady=(0, 30))
        
        # AB Map Tool button
        ab_map_frame = ttk.LabelFrame(tools_frame, text="AB Map Tool", padding="20")
        ab_map_frame.grid(row=0, column=0, padx=(0, 20), sticky=(tk.W, tk.E, tk.N, tk.S))
        
        ab_map_desc = ttk.Label(ab_map_frame, 
                               text="Compare two MAP files (A vs B)\n"
                                    "• Side-by-side comparison\n"
                                    "• Detailed analysis reports\n"
                                    "• Color-coded results\n"
                                    "• Rotation support",
                               justify=tk.LEFT)
        ab_map_desc.grid(row=0, column=0, pady=(0, 15))
        
        ttk.Button(ab_map_frame, text="Launch AB Map Tool", 
                  command=self.launch_ab_map_tool,
                  style="Accent.TButton").grid(row=1, column=0)
        
        # Full Map Tool button
        full_map_frame = ttk.LabelFrame(tools_frame, text="Full Map Tool", padding="20")
        full_map_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        full_map_desc = ttk.Label(full_map_frame, 
                                 text="Process multiple MAP files\n"
                                      "• Batch processing\n"
                                      "• Multiple sheets in one Excel\n"
                                      "• File order management\n"
                                      "• Folder import support",
                                 justify=tk.LEFT)
        full_map_desc.grid(row=0, column=0, pady=(0, 15))
        
        ttk.Button(full_map_frame, text="Launch Full Map Tool", 
                  command=self.launch_full_map_tool,
                  style="Accent.TButton").grid(row=1, column=0)
        
        # Configure grid weights
        tools_frame.columnconfigure(0, weight=1)
        tools_frame.columnconfigure(1, weight=1)
        
        # Information section
        info_frame = ttk.LabelFrame(self.main_frame, text="Information", padding="15")
        info_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))
        
        info_text = ("This tool processes TSK and MAP files for semiconductor testing analysis.\n"
                    "Choose the appropriate tool based on your analysis needs:\n\n"
                    "• AB Map Tool: Best for comparing two specific files\n"
                    "• Full Map Tool: Best for processing multiple files at once")
        
        ttk.Label(info_frame, text=info_text, justify=tk.LEFT).grid(row=0, column=0, sticky=tk.W)
        
        # Bottom buttons
        button_frame = ttk.Frame(self.main_frame)
        button_frame.grid(row=4, column=0, columnspan=2, pady=(20, 0))
        
        ttk.Button(button_frame, text="Exit", 
                  command=self.exit_application).pack(side=tk.RIGHT)
        
        # Version info
        version_label = ttk.Label(self.main_frame, 
                                 text="Version 1.2.0 | Author: Yuribytes | Company: Chipone TE development Team", 
                                 font=("Arial", 8), 
                                 foreground="gray")
        version_label.grid(row=5, column=0, columnspan=2, pady=(20, 0))
        
        # Configure main frame grid weights
        self.main_frame.columnconfigure(0, weight=1)
        self.main_frame.columnconfigure(1, weight=1)
    
    def launch_ab_map_tool(self):
        """Launch AB Map Tool"""
        print("Launching AB Map Tool...")
        self.app_controller.show_ab_map_tool()
    
    def launch_full_map_tool(self):
        """Launch Full Map Tool"""
        print("Launching Full Map Tool...")
        self.app_controller.show_full_map_tool()
    
    def exit_application(self):
        """Exit the application"""
        self.app_controller.root.quit()
    
    def show(self):
        """Show the tool selector frame"""
        if self.main_frame:
            self.main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
    
    def hide(self):
        """Hide the tool selector frame"""
        if self.main_frame:
            self.main_frame.grid_remove()


def main():
    """Test function for standalone running"""
    root = tk.Tk()
    root.title("Tool Selector Test")
    root.geometry("800x700")
    
    # Mock app controller for testing
    class MockController:
        def __init__(self, root):
            self.root = root
        def show_ab_map_tool(self):
            print("Would show AB Map Tool")
        def show_full_map_tool(self):
            print("Would show Full Map Tool")
    
    controller = MockController(root)
    selector = ToolSelectorFrame(root, controller)
    selector.show()
    
    root.mainloop()


if __name__ == "__main__":
    main()
