#!/usr/bin/env python3
"""
Simple test for the bin sorting feature
Tests the sorting logic without requiring specific MAP files
"""

import os
import sys

# Add parent directory to path to import modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_sorting_logic():
    """Test the sorting logic with mock data"""
    print("🧪 Testing Bin Sorting Logic")
    print("=" * 40)
    
    try:
        from tsk_map_processor import TSKMapProcessor
        
        # Create a processor instance
        processor = TSKMapProcessor()
        
        # Create mock bin statistics data
        mock_bin_stats = [
            {'bin_name': 'bin0', 'quantity': 50, 'yield_percentage': 5.0},
            {'bin_name': 'bin1', 'quantity': 800, 'yield_percentage': 80.0},
            {'bin_name': 'bin2', 'quantity': 100, 'yield_percentage': 10.0},
            {'bin_name': 'bin5', 'quantity': 30, 'yield_percentage': 3.0},
            {'bin_name': 'bin3', 'quantity': 20, 'yield_percentage': 2.0},
        ]
        
        # Mock the map_data and other required attributes
        processor.map_data = [
            [(1, 1), (1, 1), (2, 1), (0, 1), (1, 1)],  # Row 1
            [(1, 1), (5, 1), (1, 1), (1, 1), (2, 1)],  # Row 2
            [(0, 1), (1, 1), (3, 1), (1, 1), (1, 1)],  # Row 3
            [(1, 1), (2, 1), (1, 1), (1, 1), (1, 1)],  # Row 4
        ]
        processor.rowsize = 4
        processor.columnsize = 5
        
        print("📊 Mock data created:")
        print("   bin0: 50 dies (5.0%)")
        print("   bin1: 800 dies (80.0%)")
        print("   bin2: 100 dies (10.0%)")
        print("   bin3: 20 dies (2.0%)")
        print("   bin5: 30 dies (3.0%)")
        
        # Test 1: Sort by quantity (descending)
        print("\n1️⃣ Testing sort by quantity (descending)...")
        bin_stats_qty = processor.get_bin_statistics(sort_by_quantity=True)
        
        print("   Result order:")
        for i, bin_data in enumerate(bin_stats_qty, 1):
            print(f"   {i}. {bin_data['bin_name']}: {bin_data['quantity']} dies")
        
        # Verify quantity sorting
        quantities = [bin_data['quantity'] for bin_data in bin_stats_qty]
        is_qty_sorted = all(quantities[i] >= quantities[i+1] for i in range(len(quantities)-1))
        
        if is_qty_sorted:
            print("   ✅ Correctly sorted by quantity (descending)")
        else:
            print("   ❌ NOT correctly sorted by quantity")
            return False
        
        # Test 2: Sort by bin number (ascending)
        print("\n2️⃣ Testing sort by bin number (ascending)...")
        bin_stats_num = processor.get_bin_statistics(sort_by_quantity=False)
        
        print("   Result order:")
        for i, bin_data in enumerate(bin_stats_num, 1):
            bin_number = int(bin_data['bin_name'].replace('bin', ''))
            print(f"   {i}. {bin_data['bin_name']} (#{bin_number}): {bin_data['quantity']} dies")
        
        # Verify number sorting
        bin_numbers = [int(bin_data['bin_name'].replace('bin', '')) for bin_data in bin_stats_num]
        is_num_sorted = all(bin_numbers[i] <= bin_numbers[i+1] for i in range(len(bin_numbers)-1))
        
        if is_num_sorted:
            print("   ✅ Correctly sorted by bin number (ascending)")
        else:
            print("   ❌ NOT correctly sorted by bin number")
            return False
        
        # Test 3: Verify expected order
        print("\n3️⃣ Verifying expected orders...")
        
        # Expected quantity order: bin1(800), bin2(100), bin0(50), bin5(30), bin3(20)
        expected_qty_order = ['bin1', 'bin2', 'bin0', 'bin5', 'bin3']
        actual_qty_order = [bin_data['bin_name'] for bin_data in bin_stats_qty]
        
        if actual_qty_order == expected_qty_order:
            print("   ✅ Quantity sort order matches expected")
        else:
            print(f"   ❌ Quantity sort order mismatch")
            print(f"      Expected: {expected_qty_order}")
            print(f"      Actual:   {actual_qty_order}")
            return False
        
        # Expected number order: bin0, bin1, bin2, bin3, bin5
        expected_num_order = ['bin0', 'bin1', 'bin2', 'bin3', 'bin5']
        actual_num_order = [bin_data['bin_name'] for bin_data in bin_stats_num]
        
        if actual_num_order == expected_num_order:
            print("   ✅ Number sort order matches expected")
        else:
            print(f"   ❌ Number sort order mismatch")
            print(f"      Expected: {expected_num_order}")
            print(f"      Actual:   {actual_num_order}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_gui_integration():
    """Test GUI integration without actually showing the GUI"""
    print("\n🖥️  Testing GUI Integration")
    print("=" * 40)
    
    try:
        import tkinter as tk
        from full_map_tool_frame import FullMapToolFrame
        
        # Create hidden root window
        root = tk.Tk()
        root.withdraw()
        
        class MockController:
            def __init__(self):
                self.root = root
        
        controller = MockController()
        
        # Create frame
        frame = FullMapToolFrame(root, controller)
        
        # Test default value
        print("1️⃣ Testing default checkbox state...")
        if frame.sort_by_quantity.get():
            print("   ✅ Default is True (sort by quantity)")
        else:
            print("   ❌ Default should be True")
            return False
        
        # Test changing value
        print("2️⃣ Testing checkbox state changes...")
        frame.sort_by_quantity.set(False)
        if not frame.sort_by_quantity.get():
            print("   ✅ Successfully changed to False")
        else:
            print("   ❌ Failed to change to False")
            return False
        
        frame.sort_by_quantity.set(True)
        if frame.sort_by_quantity.get():
            print("   ✅ Successfully changed back to True")
        else:
            print("   ❌ Failed to change back to True")
            return False
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ Error during GUI testing: {e}")
        return False


def test_processor_integration():
    """Test FullMapProcessor integration"""
    print("\n⚙️  Testing FullMapProcessor Integration")
    print("=" * 40)
    
    try:
        from full_map_processor import FullMapProcessor
        
        # Test default setting
        print("1️⃣ Testing default setting...")
        processor = FullMapProcessor()
        if processor.sort_by_quantity:
            print("   ✅ Default sort_by_quantity is True")
        else:
            print("   ❌ Default should be True")
            return False
        
        # Test setter method
        print("2️⃣ Testing setter method...")
        processor.set_sort_by_quantity(False)
        if not processor.sort_by_quantity:
            print("   ✅ Successfully set to False")
        else:
            print("   ❌ Failed to set to False")
            return False
        
        processor.set_sort_by_quantity(True)
        if processor.sort_by_quantity:
            print("   ✅ Successfully set back to True")
        else:
            print("   ❌ Failed to set back to True")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error during processor testing: {e}")
        return False


def main():
    """Run all simple tests"""
    print("🎯 Bin Sorting Feature - Simple Test Suite")
    print("=" * 60)
    
    # Run tests
    test1 = test_sorting_logic()
    test2 = test_gui_integration()
    test3 = test_processor_integration()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST RESULTS")
    print("=" * 60)
    print(f"   Sorting Logic: {'PASS' if test1 else 'FAIL'}")
    print(f"   GUI Integration: {'PASS' if test2 else 'FAIL'}")
    print(f"   Processor Integration: {'PASS' if test3 else 'FAIL'}")
    
    all_passed = test1 and test2 and test3
    print(f"\nOVERALL: {'ALL TESTS PASSED' if all_passed else 'SOME TESTS FAILED'}")
    
    if all_passed:
        print("\n🎉 SUCCESS! Bin sorting feature is working correctly!")
        print("\n📋 Feature Summary:")
        print("   ✅ Added checkbox: 'Sort bins by quantity (descending order)'")
        print("   ✅ Default: Checked (sort by quantity - existing behavior)")
        print("   ✅ Unchecked: Sort by bin number (0,1,2... ascending)")
        print("   ✅ Affects Excel output columns A14~AXX, B14~BXX, C14~CXX")
        print("   ✅ Maintains backward compatibility")
        print("   ✅ Minimal GUI space usage")
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
